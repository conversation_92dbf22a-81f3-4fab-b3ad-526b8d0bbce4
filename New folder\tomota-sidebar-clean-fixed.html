<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Pond Report - Tomota Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        aqua: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        heading: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            background-color: #fafafa;
            color: #333;
            line-height: 1.5;
        }

        /* Sidebar Styles */
        .sidebar, .tomota-sidebar {
            width: 230px;
            height: 100vh;
            background-color: #f5f5f5;
            border-right: none;
            position: fixed;
            left: 0 !important;
            top: 0;
            margin-left: 0 !important;
            z-index: 1200;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            font-family: "Helvetica", "Roboto", Arial, sans-serif;
            transition: transform 0.3s ease;
        }

        .tomota-sidebar.hidden {
            transform: translateX(-100%);
        }

        .sidebar-logo {
            padding: 16px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
            background-color: #f5f5f5;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 77px;
        }

        .logo {
            width: 218px;
            height: 77px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border-radius: 4px;
            padding: 8px;
        }

        .logo-image {
            width: 160px;
            height: 100px;
            object-fit: contain;
            background-color: transparent;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }

        .sidebar-nav {
            flex: 1;
            padding: 8px 0;
            overflow-y: auto;
            background-color: #f5f5f5;
        }

        .nav-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px 16px;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #565656;
            font-size: 14px;
            font-weight: 400;
            text-align: left;
            font-family: inherit;
        }

        .nav-item:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .nav-item.active {
            background-color: rgba(0, 0, 0, 0.08);
            color: #303030;
            font-weight: 500;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 400;
        }

        /* Sidebar Dropdown Styles */
        .nav-item-with-dropdown {
            position: relative;
        }

        .nav-dropdown-arrow {
            width: 16px;
            height: 16px;
            transition: transform 0.2s ease;
            flex-shrink: 0;
        }

        .nav-item-with-dropdown.open .nav-dropdown-arrow {
            transform: rotate(180deg);
        }

        .nav-dropdown {
            max-height: 0;
            overflow: hidden;
            background-color: #eeeeee;
            transition: max-height 0.3s ease, padding 0.3s ease;
            margin-left: 0;
        }

        .nav-dropdown.open {
            max-height: 350px;
            padding: 8px 0;
        }

        .nav-dropdown-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 10px 16px 10px 52px;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #565656;
            font-size: 13px;
            font-weight: 400;
            text-align: left;
            font-family: inherit;
        }

        .nav-dropdown-item:hover {
            background-color: rgba(0, 0, 0, 0.06);
        }

        .nav-dropdown-item.active {
            background-color: rgba(0, 0, 0, 0.1);
            color: #303030;
            font-weight: 500;
        }

        /* Header Styles */
        .header, .header-section {
            height: 110px;
            background: linear-gradient(90deg, rgba(0, 148, 255, 0.3) 0%, rgba(223, 216, 219, 0.3) 50%, rgba(255, 203, 191, 0.3) 100%);
            color: #2c3e50;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 230px;
            right: 0;
            z-index: 9999;
            transition: left 0.3s ease;
        }

        .header-section.sidebar-hidden {
            left: 0;
        }

        .toolbar {
            display: flex;
            align-items: center;
            padding: 0 24px;
            height: 100%;
        }

        .toolbar-grid {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .left-section {
            display: flex;
            align-items: center;
            flex: 1;
            gap: 20px;
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .menu-button {
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(44, 62, 80, 0.2);
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: #2c3e50;
        }

        .menu-button:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: scale(1.05);
        }

        .menu-button svg {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        .farm-info {
            color: #2c3e50;
        }

        .farm-info-label {
            font-size: 10px;
            color: rgba(44, 62, 80, 0.7);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .farm-info-name {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .header-icon-button {
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(44, 62, 80, 0.2);
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: #2c3e50;
        }

        .header-icon-button:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: translateY(-2px);
        }

        .header-icon-button img {
            width: 20px;
            height: 20px;
        }

        .header-flag {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            object-fit: cover;
            border: 2px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .header-flag:hover {
            transform: scale(1.05);
            border-color: #3b82f6;
        }

        .avatar-button {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            background-color: #1976d2;
        }

        .avatar-button:hover {
            background-color: rgba(25, 118, 210, 0.8);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Main Content Area - Clean Professional Design */
        .main-content {
            margin-left: 230px;
            margin-top: 110px;
            padding: 24px;
            transition: margin-left 0.3s ease;
            background: #f8fafc;
            min-height: calc(100vh - 110px);
        }

        .main-content.sidebar-hidden {
            margin-left: 0;
        }

        .content-container {
            max-width: 100%;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* Clean Dashboard Header */
        .dashboard-header {
            padding: 32px 32px 24px;
            background: white;
            border-bottom: 1px solid #e2e8f0;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 600;
            color: #1a202c;
            margin: 0 0 8px 0;
        }

        .dashboard-subtitle {
            color: #718096;
            font-size: 1rem;
            margin: 0;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            padding: 24px 32px;
            background: #f7fafc;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            border-color: #cbd5e0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .stat-icon.active { background: #c6f6d5; color: #22543d; }
        .stat-icon.stocking { background: #bee3f8; color: #2a4365; }
        .stat-icon.renovation { background: #e9d8fd; color: #553c9a; }
        .stat-icon.risk { background: #fed7d7; color: #c53030; }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a202c;
            line-height: 1;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-change.positive { color: #38a169; }
        .stat-change.negative { color: #e53e3e; }
        .stat-change.neutral { color: #718096; }

        /* Main Content Area */
        .main-dashboard {
            padding: 32px;
            background: white;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a202c;
            margin: 0;
        }

        .section-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #3182ce;
            color: white;
            border-color: #3182ce;
        }

        .btn-primary:hover {
            background: #2c5282;
            border-color: #2c5282;
        }

        .btn-secondary {
            background: white;
            color: #4a5568;
            border-color: #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f7fafc;
            border-color: #cbd5e0;
        }

        /* Data Table */
        .data-table-container {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: #f7fafc;
            padding: 12px 16px;
            text-align: left;
            font-size: 0.75rem;
            font-weight: 600;
            color: #4a5568;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table td {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 0.875rem;
            color: #2d3748;
        }

        .data-table tr:hover {
            background: #f7fafc;
        }

        /* Interactive Elements */
        .interactive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .interactive-item {
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .interactive-item:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.05);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .interactive-item-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
            margin-bottom: 8px;
        }

        .interactive-item-label {
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.8;
        }

        /* Modern Data Visualization */
        .data-viz-container {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            position: relative;
        }

        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
            position: relative;
        }

        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .progress-ring-bg {
            fill: none;
            stroke: #e2e8f0;
            stroke-width: 8;
        }

        .progress-ring-fill {
            fill: none;
            stroke: url(#gradient);
            stroke-width: 8;
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
        }

        /* Floating Action Buttons */
        .fab-container {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .fab {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
        }

        .fab.primary {
            width: 70px;
            height: 70px;
            font-size: 1.8rem;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        /* Modern Tabs */
        .modern-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 5px;
            margin: 30px 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modern-tab {
            flex: 1;
            padding: 15px 25px;
            border: none;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .modern-tab.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            backdrop-filter: blur(20px);
        }

        .modern-tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .content-sections {
                grid-template-columns: 1fr;
                padding: 30px;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-stats {
                gap: 20px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
            }

            .hero-section {
                padding: 40px 20px;
            }

            .hero-title {
                font-size: 2rem;
            }

            .hero-stats {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }

            .content-sections {
                padding: 20px;
                gap: 20px;
            }

            .content-card {
                padding: 20px;
            }

            .modern-tabs {
                margin: 20px;
                flex-direction: column;
            }

            .fab-container {
                bottom: 20px;
                right: 20px;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .slide-up {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Breadcrumbs */
        .breadcrumbs {
            padding: 16px 24px 0;
            font-size: 14px;
            color: #6c757d;
        }

        .breadcrumbs a {
            color: #007bff;
            text-decoration: none;
        }

        .breadcrumbs a:hover {
            text-decoration: underline;
        }

        /* Content Header */
        .content-header {
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .content-header h2 {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            color: #333;
        }

        /* Controls */
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .farm-selector {
            display: flex;
            flex-direction: column;
        }

        .farm-selector label {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #555;
        }

        .farm-selector select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            min-width: 200px;
        }

        .date-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-controls input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            width: 120px;
            text-align: center;
        }

        .date-controls button {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .date-controls button:hover {
            background: #f8f9fa;
        }

        /* Tabs */
        .content-tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            padding: 0 24px;
            background: #f8f9fa;
        }

        .content-tab {
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            margin-bottom: -1px;
            text-decoration: none;
            color: inherit;
        }

        .content-tab.active {
            color: #ff6b00;
            border-bottom-color: #ff6b00;
        }

        .content-tab:not(.active):hover {
            color: #007bff;
        }

        /* Legend */
        .legend {
            display: flex;
            gap: 24px;
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            display: inline-block;
            margin-right: 8px;
        }

        /* Status Colors - Match pond colors exactly */
        .active-pond {
            background-color: #219653 !important;
        }
        .stocking-pond {
            background-color: #3ac1ff !important;
        }
        .renovation-pond {
            background-color: #03256b !important;
        }
        .risk-pond-1 {
            background-color: #ce262d !important;
        }
        .risk-pond-2 {
            background-color: #ffcc00 !important;
        }
        .risk-pond-3 {
            background-color: #ffff00 !important;
        }

        /* Table */
        .table-container {
            overflow-x: auto;
            padding: 0 24px 24px;
            width: 100%;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1600px;
        }

        th, td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
            font-size: 13px;
            white-space: nowrap;
            min-width: 40px;
        }

        thead th {
            background: #f1f3f5;
            font-weight: 500;
            color: #495057;
            position: sticky;
            top: 0;
        }

        .pond-cell {
            background: #c7d6e3;
            min-width: 40px;
        }

        .pond-cell.active {
            background: white;
            border: 1px solid #000;
        }

        .pond-number {
            font-size: 12px;
        }

        /* Tab Content Styling */
        .tab-content {
            display: block;
        }

        .tab-content.hidden {
            display: none;
        }

        /* Additional styles for better table appearance */
        .table-container table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1600px;
        }

        .table-container th, .table-container td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
            font-size: 13px;
            white-space: nowrap;
            min-width: 40px;
        }

        .table-container thead th {
            background: #f1f3f5;
            font-weight: 500;
            color: #495057;
            position: sticky;
            top: 0;
        }

        /* Additional styles for AquaPond Analytics */
        .header-gradient {
            background: linear-gradient(135deg, #0ea5e9 0%, #0d9488 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .status-active {
            background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            color: white;
        }
        .status-stocking {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
            color: white;
        }
        .status-renovation {
            background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);
            color: white;
        }
        .status-risk {
            background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
            color: white;
        }
        .table-header {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
        .hover-row:hover {
            background-color: #f0f9ff;
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
        }
        .progress-bg {
            background-color: #e2e8f0;
        }
        .progress-fill {
            background: linear-gradient(90deg, #0ea5e9, #0d9488);
        }
        .summary-card {
            transform: translateY(0);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-5px);
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(14, 165, 233, 0); }
            100% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0); }
        }
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        /* User Dropdown Menu Styles */
        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1), 0 5px 8px -5px rgba(0, 0, 0, 0.04);
            min-width: 260px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 8px;
            overflow: hidden;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 16px 20px 12px 20px;
            text-align: center;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
        }

        .dropdown-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px auto;
            box-shadow: 0 3px 8px rgba(245, 158, 11, 0.3);
        }

        .dropdown-avatar svg {
            width: 30px;
            height: 30px;
            color: white;
        }

        .dropdown-header h3 {
            margin: 0 0 2px 0;
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }

        .dropdown-header p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
        }

        .dropdown-section {
            padding: 0;
        }

        .dropdown-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: #f9fafb;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dropdown-item-icon {
            width: 16px;
            height: 16px;
            color: #6b7280;
        }

        .dropdown-item-label {
            font-size: 13px;
            color: #374151;
            font-weight: 400;
        }

        .dropdown-item-value {
            font-size: 12px;
            color: #6b7280;
            background: #f59e0b;
            color: white;
            padding: 2px 8px;
            border-radius: 16px;
            font-weight: 500;
        }

        .dropdown-action {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 13px;
            color: #374151;
            font-weight: 400;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 1px solid #f3f4f6;
        }

        .dropdown-action:hover {
            background-color: #f9fafb;
        }

        .dropdown-action:last-child {
            border-bottom: none;
        }

        .dropdown-action-icon {
            width: 16px;
            height: 16px;
            color: #6b7280;
        }

        .dropdown-action.logout {
            color: #dc2626;
            border-top: 1px solid #f3f4f6;
        }

        .dropdown-action.logout:hover {
            background-color: #fef2f2;
        }

        .dropdown-action.logout .dropdown-action-icon {
            color: #dc2626;
        }

        /* Modern Avatar Styles */
        .user-avatar-modern {
            position: relative;
            width: 44px;
            height: 44px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-avatar-modern:hover {
            transform: scale(1.05);
        }

        .avatar-inner {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .avatar-letter {
            font-size: 18px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            z-index: 3;
            position: relative;
        }

        .avatar-ring {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 2px solid transparent;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 300%;
            animation: gradientShift 3s ease infinite;
            z-index: 1;
        }

        .avatar-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse 2s ease-in-out infinite;
            z-index: 0;
        }

        .avatar-status {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 14px;
            height: 14px;
            background: linear-gradient(135deg, #4ade80, #22c55e);
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            z-index: 4;
        }

        .avatar-status::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: statusPulse 1.5s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.7; transform: translate(-50%, -50%) scale(0.8); }
        }

        /* Avatar button hover effects */
        .avatar-button {
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .avatar-button:hover .avatar-inner {
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .avatar-button:hover .avatar-glow {
            animation: pulse 1s ease-in-out infinite;
        }

        .avatar-button:active .user-avatar-modern {
            transform: scale(0.95);
        }

        /* Dashboard Styles */
        .dashboard-section {
            background: #f8f9fa;
            min-height: calc(100vh - 120px);
            padding: 0;
        }

        .dashboard-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .farm-selector-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .farm-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            min-width: 40px;
        }

        .farm-dropdown {
            position: relative;
        }

        .farm-select {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 32px 8px 12px;
            font-size: 14px;
            color: #333;
            min-width: 200px;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
        }

        .farm-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .dashboard-main {
            padding: 24px;
        }

        .breadcrumb-section {
            margin-bottom: 16px;
        }

        .breadcrumb-nav {
            font-size: 14px;
            color: #666;
        }

        .breadcrumb-item {
            color: #666;
        }

        .title-and-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 16px;
        }

        .dashboard-title {
            font-size: 32px;
            font-weight: 400;
            color: #333;
            margin: 0;
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
        }

        .filter-controls {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .filter-group {
            position: relative;
        }

        .filter-select {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 32px 8px 12px;
            font-size: 14px;
            color: #666;
            min-width: 120px;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .filter-select:hover {
            border-color: #bbb;
        }

        /* Map container adjustments for dashboard */
        .dashboard-section .map-container {
            margin: 0 24px 24px 24px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .title-and-filters {
                flex-direction: column;
                align-items: flex-start;
            }

            .filter-controls {
                width: 100%;
                flex-wrap: wrap;
            }

            .filter-group {
                flex: 1;
                min-width: 100px;
            }

            .filter-select {
                width: 100%;
            }
        }

        .MuiBox-root {
            box-sizing: border-box;
        }

        .jss73 {
            padding: 24px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .jss74 {
            margin-bottom: 24px;
        }

        .jss75 {
            margin-bottom: 16px;
        }

        .MuiBreadcrumbs-root {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            font-size: 0.875rem;
            line-height: 1.43;
            letter-spacing: 0.01071em;
            color: rgba(0, 0, 0, 0.6);
        }

        .MuiBreadcrumbs-ol {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .MuiBreadcrumbs-li {
            display: flex;
            align-items: center;
        }

        .jss76 {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
        }

        .jss78 {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .jss79 {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            font-weight: 400;
            font-size: 2.125rem;
            line-height: 1.235;
            letter-spacing: 0.00735em;
            color: rgba(0, 0, 0, 0.87);
            margin: 0;
        }

        .jss80 {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .jss81 {
            width: 100%;
        }

        .MuiContainer-root {
            width: 100%;
            margin-left: auto;
            margin-right: auto;
            padding-left: 16px;
            padding-right: 16px;
        }

        .MuiContainer-maxWidthLg {
            max-width: 1280px;
        }

        .jss84 {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .jss85 {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .jss86, .jss93, .jss95 {
            min-width: 200px;
            flex: 1;
        }

        .MuiAutocomplete-root {
            position: relative;
            display: inline-flex;
            vertical-align: top;
        }

        .MuiFormControl-root {
            display: inline-flex;
            flex-direction: column;
            position: relative;
            min-width: 0;
            padding: 0;
            margin: 0;
            border: 0;
            vertical-align: top;
        }

        .MuiFormControl-fullWidth {
            width: 100%;
        }

        .MuiInputLabel-root {
            color: rgba(0, 0, 0, 0.54);
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            font-weight: 400;
            font-size: 1rem;
            line-height: 1;
            letter-spacing: 0.00938em;
            padding: 0;
            position: absolute;
            left: 0;
            top: 0;
            transform: translate(14px, 16px) scale(1);
            transition: color 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms,transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
            z-index: 1;
            pointer-events: none;
        }

        .MuiInputLabel-outlined {
            transform: translate(14px, 20px) scale(1);
        }

        .MuiInputLabel-marginDense {
            transform: translate(14px, 12px) scale(1);
        }

        .MuiInputBase-root {
            color: rgba(0, 0, 0, 0.87);
            cursor: text;
            display: inline-flex;
            position: relative;
            font-size: 1rem;
            box-sizing: border-box;
            align-items: center;
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            font-weight: 400;
            line-height: 1.1876em;
            letter-spacing: 0.00938em;
        }

        .MuiOutlinedInput-root {
            position: relative;
            border-radius: 4px;
        }

        .MuiInputBase-fullWidth {
            width: 100%;
        }

        .MuiInputBase-marginDense {
            padding-top: 12px;
            padding-bottom: 12px;
        }

        .MuiInputBase-input {
            font: inherit;
            letter-spacing: inherit;
            color: currentColor;
            padding: 18.5px 14px;
            border: 0;
            box-sizing: content-box;
            background: none;
            height: 1.1876em;
            margin: 0;
            display: block;
            min-width: 0;
            width: 100%;
            animation-name: mui-auto-fill-cancel;
            animation-duration: 10ms;
        }

        .MuiOutlinedInput-input {
            padding: 18.5px 14px;
        }

        .MuiInputBase-inputMarginDense {
            padding-top: 12px;
            padding-bottom: 12px;
        }

        .MuiOutlinedInput-inputMarginDense {
            padding-top: 12px;
            padding-bottom: 12px;
        }

        .MuiInputBase-inputAdornedEnd {
            padding-right: 0;
        }

        .MuiOutlinedInput-inputAdornedEnd {
            padding-right: 0;
        }

        .MuiAutocomplete-endAdornment {
            position: absolute;
            right: 9px;
            top: calc(50% - 12px);
        }

        .MuiIconButton-root {
            flex: 0 0 auto;
            color: rgba(0, 0, 0, 0.54);
            padding: 12px;
            overflow: visible;
            font-size: 1.5rem;
            text-align: center;
            transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            border-radius: 50%;
            background-color: transparent;
            border: none;
            cursor: pointer;
        }

        .MuiIconButton-root:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .MuiSvgIcon-root {
            fill: currentColor;
            width: 1em;
            height: 1em;
            display: inline-block;
            font-size: 1.5rem;
            transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            flex-shrink: 0;
            user-select: none;
        }

        .MuiSvgIcon-fontSizeSmall {
            font-size: 1.25rem;
        }

        .MuiOutlinedInput-notchedOutline {
            border-color: rgba(0, 0, 0, 0.23);
            border-style: solid;
            border-width: 1px;
            border-radius: inherit;
            position: absolute;
            top: -5px;
            left: 0;
            right: 0;
            bottom: 0;
            margin: 0;
            padding: 0 8px;
            pointer-events: none;
        }

        .jss89 {
            border-color: rgba(0, 0, 0, 0.23);
        }

        .jss91 {
            width: auto;
            height: 11px;
            display: block;
            padding: 0;
            font-size: 0.75em;
            max-width: 0.01px;
            text-align: left;
            transition: max-width 50ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
            visibility: hidden;
        }

        .jss91 span {
            padding-left: 5px;
            padding-right: 5px;
            display: inline-block;
        }

        .jss97 {
            margin-top: 32px;
        }

        .jss98 {
            height: 200px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .jss98::before {
            content: "Dashboard Content Area";
            color: #666;
            font-size: 18px;
            font-weight: 500;
        }

        /* Map Section Styles */
        .map-container {
            margin-top: 32px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .map-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .map-controls {
            display: flex;
            gap: 8px;
        }

        .map-control-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .map-control-btn:hover {
            background: #f0f0f0;
            border-color: #bbb;
        }

        .map-control-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .map-wrapper {
            display: flex;
            height: 600px;
        }

        .farm-map {
            flex: 1;
            position: relative;
            background: #f0f8ff;
        }

        .map-placeholder {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
        }

        .map-loading {
            max-width: 400px;
        }

        .map-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin: 24px 0;
            width: 100%;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            font-size: 14px;
            color: #555;
        }

        .feature-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .feature-icon.active-pond {
            background: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
        }

        .feature-icon.stocking-pond {
            background: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3);
        }

        .feature-icon.renovation-pond {
            background: #6f42c1;
            box-shadow: 0 0 0 2px rgba(111, 66, 193, 0.3);
        }

        .feature-icon.risk-pond {
            background: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
        }

        .load-map-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .load-map-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }

        .map-sidebar {
            width: 320px;
            background: #f8f9fa;
            border-left: 1px solid #e0e0e0;
            overflow-y: auto;
        }

        .pond-list {
            padding: 20px;
        }

        .pond-list-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0 0 16px 0;
        }

        .pond-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: white;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #e0e0e0;
        }

        .pond-item:hover {
            background: #f0f8ff;
            border-color: #007bff;
            transform: translateX(4px);
        }

        .pond-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .pond-status.active {
            background: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
        }

        .pond-status.stocking {
            background: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3);
        }

        .pond-status.renovation {
            background: #6f42c1;
            box-shadow: 0 0 0 2px rgba(111, 66, 193, 0.3);
        }

        .pond-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .pond-name {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .pond-details {
            font-size: 12px;
            color: #666;
        }

        .pond-item i {
            color: #999;
            font-size: 12px;
        }

        /* Satellite Map Styles */
        .satellite-map-container {
            margin: 24px 0;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
            width: 100% !important;
            max-width: none !important;
            height: 800px !important;
        }

        .map-view-controls {
            position: absolute;
            top: 16px;
            left: 16px;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-toggle {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .view-btn.active {
            background: #3b82f6;
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        .fullscreen-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            z-index: 10;
            padding: 10px;
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 8px;
            color: #64748b;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        .fullscreen-btn:hover {
            background: white;
            color: #3b82f6;
        }

        .satellite-map-wrapper {
            height: 800px !important;
            position: relative;
            min-height: 700px;
            width: 100%;
        }

        .satellite-map-view {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .satellite-background {
            width: 100%;
            height: 100%;
            position: relative;
            background: linear-gradient(135deg, #2d5a3d 0%, #1a3d2e 50%, #0f2419 100%);
        }

        .location-marker {
            z-index: 5;
        }

        .marker-pin {
            width: 24px;
            height: 24px;
            background: #ef4444;
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
        }

        .marker-pin i {
            transform: rotate(45deg);
            color: white;
            font-size: 12px;
        }

        .marker-label {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .location-marker:hover .marker-label {
            opacity: 1;
        }

        .pond-marker {
            z-index: 4;
            cursor: pointer;
        }

        .pond-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
        }

        .active-marker .pond-dot {
            background: #10b981;
        }

        .stocking-marker .pond-dot {
            background: #3b82f6;
        }

        .renovation-marker .pond-dot {
            background: #f59e0b;
        }

        .risk-marker .pond-dot {
            background: #ef4444;
        }

        .pond-marker:hover .pond-dot {
            transform: scale(1.2);
        }

        .pond-tooltip {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
        }

        .pond-marker:hover .pond-tooltip {
            opacity: 1;
        }

        .map-zoom-controls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 6px;
            color: #64748b;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        .zoom-btn:hover {
            background: white;
            color: #3b82f6;
            transform: scale(1.05);
        }

        /* Responsive design for map */
        @media (max-width: 768px) {
            .map-wrapper {
                flex-direction: column;
                height: auto;
            }

            .farm-map {
                height: 400px;
            }

            .map-sidebar {
                width: 100%;
                max-height: 300px;
            }

            .map-header {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }

            .map-features {
                grid-template-columns: 1fr;
            }

            .satellite-map-wrapper {
                height: 600px !important;
                min-height: 500px;
            }

            .satellite-map-container {
                height: 600px !important;
            }

            .satellite-map-container {
                margin: 16px 0;
                border-radius: 12px;
            }

            .map-view-controls {
                position: absolute;
                top: 12px;
                left: 12px;
                margin: 0;
            }

            .view-toggle {
                padding: 2px;
            }

            .view-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .fullscreen-btn {
                top: 12px;
                right: 12px;
                padding: 8px;
            }

            .zoom-btn {
                width: 36px;
                height: 36px;
            }

            .map-zoom-controls {
                bottom: 16px;
                right: 16px;
            }
        }

        /* AquaPond Analytics Styles */
        .aquapond-filters {
            background-color: #f8fafc;
        }

        .jss59 {
            background: linear-gradient(90deg, rgba(0, 148, 255, 0.3) 0%, rgba(223, 216, 219, 0.3) 50%, rgba(255, 203, 191, 0.3) 100%);
            min-height: 95px;
        }

        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .autocomplete-container {
            position: relative;
        }

        .autocomplete-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: all 0.2s;
            background-color: white;
        }

        .autocomplete-input:focus {
            outline: none;
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .autocomplete-label {
            display: block;
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            margin-bottom: 4px;
        }

        /* Tailwind-like utility classes for the filters */
        .max-w-7xl {
            max-width: 80rem;
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .sm\\:px-6 {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        .lg\\:px-8 {
            padding-left: 2rem;
            padding-right: 2rem;
        }

        .py-8 {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        .mb-8 {
            margin-bottom: 2rem;
        }

        .bg-white {
            background-color: white;
        }

        .rounded-xl {
            border-radius: 0.75rem;
        }

        .p-6 {
            padding: 1.5rem;
        }

        .grid {
            display: grid;
        }

        .grid-cols-1 {
            grid-template-columns: repeat(1, minmax(0, 1fr));
        }

        .gap-4 {
            gap: 1rem;
        }

        .relative {
            position: relative;
        }

        .absolute {
            position: absolute;
        }

        .inset-y-0 {
            top: 0;
            bottom: 0;
        }

        .right-0 {
            right: 0;
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .pr-3 {
            padding-right: 0.75rem;
        }

        .text-gray-400 {
            color: #9ca3af;
        }

        /* Farm List Styles - New Tailwind-based Design */
        .farm-content {
            background: #f8fafc;
            min-height: calc(100vh - 130px);
            font-family: 'Inter', sans-serif;
        }

        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .table-container {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            overflow: hidden;
            background: white;
        }

        .table-header {
            background-color: #f9fafb;
        }

        .table-row:hover {
            background-color: #f3f4f6;
        }

        .action-icon {
            cursor: pointer;
            color: #6b7280;
            transition: color 0.2s ease-in-out;
            padding: 4px;
            border-radius: 4px;
        }

        .action-icon:hover {
            color: #0ea5e9;
            background-color: #f0f9ff;
        }

        /* Breadcrumb styles */
        .breadcrumb-nav {
            margin-bottom: 1rem;
        }

        .breadcrumb-nav ol {
            list-style: none;
            display: flex;
            align-items: center;
            padding: 0;
            margin: 0;
        }

        .breadcrumb-nav a {
            color: #0ea5e9;
            text-decoration: none;
        }

        .breadcrumb-nav a:hover {
            color: #0284c7;
        }

        .breadcrumb-separator {
            margin: 0 0.5rem;
            color: #6b7280;
        }

        /* Search and action styles */
        .search-actions-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .search-container {
            position: relative;
            width: 100%;
            max-width: 32rem;
        }

        .search-input {
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            padding-right: 2.5rem;
            width: 100%;
            font-size: 0.875rem;
            transition: border-color 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .search-button {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
        }

        .action-buttons-container {
            display: flex;
            gap: 1rem;
        }

        .action-button {
            background-color: #ea580c;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .action-button:hover {
            background-color: #dc2626;
        }

        /* Table styles */
        .farm-table {
            width: 100%;
            border-collapse: collapse;
        }

        .farm-table th {
            padding: 0.75rem 1rem;
            text-align: left;
            font-size: 0.875rem;
            font-weight: 500;
            color: #111827;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background-color: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .farm-table td {
            padding: 0.75rem 1rem;
            white-space: nowrap;
            font-size: 0.875rem;
            color: #6b7280;
            border-bottom: 1px solid #e5e7eb;
        }

        .user-avatar {
            height: 2rem;
            width: 2rem;
            border-radius: 50%;
            background-color: #d1d5db;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .table-actions {
            display: flex;
            gap: 1rem;
        }

        /* Pagination styles */
        .pagination-container {
            margin-top: 1rem;
            display: flex;
            justify-content: flex-end;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .pagination-text {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .pagination-select {
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            padding: 0.25rem 0.5rem;
            margin-left: 0.5rem;
        }

        .pagination-button {
            padding: 0.25rem 0.5rem;
            border-radius: 0.5rem;
            background-color: #e5e7eb;
            border: none;
            cursor: pointer;
            margin-left: 0.5rem;
            transition: background-color 0.2s;
        }

        .pagination-button:hover {
            background-color: #d1d5db;
        }

        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .search-actions-container {
                flex-direction: column;
                align-items: stretch;
            }

            .action-buttons-container {
                justify-content: center;
            }

            .table-container {
                overflow-x: auto;
            }

            .pagination-container {
                justify-content: center;
            }
        }

        /* Responsive grid for medium screens and up */
        @media (min-width: 768px) {
            .md\\:grid-cols-3 {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }

        /* Responsive padding for small screens and up */
        @media (min-width: 640px) {
            .sm\\:px-6 {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }
        }

        /* Responsive padding for large screens and up */
        @media (min-width: 1024px) {
            .lg\\:px-8 {
                padding-left: 2rem;
                padding-right: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Tomota Sidebar -->
    <div class="tomota-sidebar">
        <!-- Logo Section -->
        <div class="sidebar-logo">
            <div class="logo-container">
                <div class="logo">
                    <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/Ferris%20wheel.svg" alt="Ferris Wheel Logo" class="logo-image">
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <button class="nav-item" onclick="setActive(this); showDashboard();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-home-page-64.png" alt="Dashboard" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Dashboard</span>
            </button>

            <button class="nav-item active" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <svg style="width: 34px; height: 30px; fill: #666;" viewBox="0 0 24 24">
                        <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
                    </svg>
                </div>
                <span class="nav-text">Active pond report</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-ranking-100%20(1).png" alt="Ponds ranking" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Ponds ranking</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-stir-48.png" alt="Food mixing center" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Food mixing center</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-worker-67.png" alt="Work management" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Work management</span>
            </button>

            <div class="nav-item-with-dropdown">
                <button class="nav-item" onclick="toggleDropdown(this, 'farm-system');">
                    <div class="nav-icon">
                        <img src="file:///C:/Users/<USER>/Downloads/location.png" alt="Farm System" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                    </div>
                    <span class="nav-text">Farm System</span>
                    <svg class="nav-dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5H7z"/>
                    </svg>
                </button>
                <div class="nav-dropdown">
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showFarm();">
                        <span>Farm</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showPond();">
                        <span>Pond</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showShrimpDiary();">
                        <span>Shrimp Diary</span>
                    </button>
                </div>
            </div>

            <div class="nav-item-with-dropdown">
                <button class="nav-item" onclick="toggleDropdown(this, 'payment');">
                    <div class="nav-icon">
                        <img src="file:///C:/Users/<USER>/Downloads/—Pngtree—indian%20rupee%20note%20icon%20png_6668571.png" alt="Payment" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                    </div>
                    <span class="nav-text">Payment</span>
                    <svg class="nav-dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5H7z"/>
                    </svg>
                </button>
                <div class="nav-dropdown">
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showMine();">
                        <span>Mine</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showStaff();">
                        <span>Staff</span>
                    </button>
                </div>
            </div>

            <div class="nav-item-with-dropdown">
                <button class="nav-item" onclick="toggleDropdown(this, 'settings');">
                    <div class="nav-icon">
                        <img src="file:///C:/Users/<USER>/Downloads/Settings%20wheel.svg" alt="Setting" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                    </div>
                    <span class="nav-text">Setting</span>
                    <svg class="nav-dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5H7z"/>
                    </svg>
                </button>
                <div class="nav-dropdown">
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showListOfMaterials();">
                        <span>List of materials</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showUnitsConfig();">
                        <span>Units config</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showSeason();">
                        <span>Season</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showFarmingProcess();">
                        <span>Farming process</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showApprovalProcess();">
                        <span>Approval process</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showImportExportFile();">
                        <span>Import/export file</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showCertificationProfile();">
                        <span>Certification Profile</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showSwitchAppMode();">
                        <span>Switch app mode</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showToggleFeature();">
                        <span>Toggle Feature</span>
                    </button>
                </div>
            </div>
        </nav>
    </div>

    <!-- Header Section -->
    <div class="header-section">
        <div class="toolbar">
            <div class="toolbar-grid">
                <div class="left-section">
                    <button class="menu-button" onclick="toggleSidebar()">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </button>
                    <div class="farm-info">
                        <div class="farm-info-label">Current Farm</div>
                        <div class="farm-info-name">Sikindar Shaik</div>
                    </div>
                </div>
                <div class="right-section">
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/bell-svgrepo-com.svg" alt="Notifications">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/Ferris wheel.svg" alt="Dashboard">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/icons8-alert-48.png" alt="Alerts">
                    </button>
                    <img src="https://upload.wikimedia.org/wikipedia/commons/4/41/Flag_of_India.svg" alt="Flag of India" class="header-flag">
                    <div class="user-dropdown">
                        <button class="avatar-button" onclick="toggleUserDropdown()">
                            <div class="user-avatar-modern">
                                <div class="avatar-inner">
                                    <span class="avatar-letter">D</span>
                                    <div class="avatar-ring"></div>
                                    <div class="avatar-glow"></div>
                                </div>
                                <div class="avatar-status"></div>
                            </div>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <div class="dropdown-header">
                                <div class="dropdown-avatar">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                    </svg>
                                </div>
                                <h3>Sikindar Shaik</h3>
                                <p>No information found</p>
                            </div>
                            <div class="dropdown-section">
                                <div class="dropdown-item">
                                    <div class="dropdown-item-left">
                                        <svg class="dropdown-item-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                        </svg>
                                        <span class="dropdown-item-label"><EMAIL></span>
                                    </div>
                                </div>
                                <div class="dropdown-item">
                                    <div class="dropdown-item-left">
                                        <svg class="dropdown-item-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                        </svg>
                                        <span class="dropdown-item-label">No information found</span>
                                    </div>
                                </div>
                                <div class="dropdown-item">
                                    <div class="dropdown-item-left">
                                        <svg class="dropdown-item-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                                        </svg>
                                        <span class="dropdown-item-label">Currency unit</span>
                                    </div>
                                    <span class="dropdown-item-value">VND</span>
                                </div>
                                <div class="dropdown-action">
                                    <svg class="dropdown-action-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                    </svg>
                                    Account management
                                </div>
                                <div class="dropdown-action">
                                    <svg class="dropdown-action-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                    </svg>
                                    Management invitation
                                </div>
                                <div class="dropdown-action logout" onclick="logout()">
                                    <svg class="dropdown-action-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                                    </svg>
                                    Logout
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
        <div class="content-container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h1 class="dashboard-title">Pond Management Dashboard</h1>
                <p class="dashboard-subtitle">Monitor and manage your aquaculture operations</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Active Ponds</span>
                        <div class="stat-icon active">
                            <i class="fas fa-water"></i>
                        </div>
                    </div>
                    <div class="stat-value">8</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>12% from last month</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Stocking Ponds</span>
                        <div class="stat-icon stocking">
                            <i class="fas fa-fish"></i>
                        </div>
                    </div>
                    <div class="stat-value">4</div>
                    <div class="stat-change negative">
                        <i class="fas fa-arrow-down"></i>
                        <span>3% from last month</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Renovation</span>
                        <div class="stat-icon renovation">
                            <i class="fas fa-tools"></i>
                        </div>
                    </div>
                    <div class="stat-value">2</div>
                    <div class="stat-change neutral">
                        <i class="fas fa-minus"></i>
                        <span>No change</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">At Risk</span>
                        <div class="stat-icon risk">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="stat-value">1</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-down"></i>
                        <span>50% improvement</span>
                    </div>
                </div>
            </div>

            <!-- Main Dashboard Content -->
            <div class="main-dashboard">
                <div class="section-header">
                    <h2 class="section-title">Active Pond Overview</h2>
                    <div class="section-actions">
                        <button class="btn btn-secondary">
                            <i class="fas fa-filter"></i>
                            Filter
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Add Pond
                        </button>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Pond ID</th>
                                <th>Status</th>
                                <th>Shrimp Type</th>
                                <th>Stocking Date</th>
                                <th>Age (Days)</th>
                                <th>Density</th>
                                <th>ABW (g)</th>
                                <th>Survival %</th>
                                <th>Biomass (kg)</th>
                                <th>Feed/Day (kg)</th>
                                <th>FCR</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Pond A1</strong></td>
                                <td>
                                    <span style="display: inline-flex; align-items: center; padding: 4px 8px; background: #c6f6d5; color: #22543d; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                        <i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 4px;"></i>
                                        Active
                                    </span>
                                </td>
                                <td>Vannamei</td>
                                <td>2025-06-15</td>
                                <td>44</td>
                                <td>12.5 pcs/m²</td>
                                <td>28.4</td>
                                <td>89%</td>
                                <td>1,250</td>
                                <td>45.2</td>
                                <td>1.25</td>
                                <td>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 0.75rem;">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Pond A2</strong></td>
                                <td>
                                    <span style="display: inline-flex; align-items: center; padding: 4px 8px; background: #c6f6d5; color: #22543d; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                        <i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 4px;"></i>
                                        Active
                                    </span>
                                </td>
                                <td>Vannamei</td>
                                <td>2025-06-20</td>
                                <td>39</td>
                                <td>13.2 pcs/m²</td>
                                <td>25.8</td>
                                <td>92%</td>
                                <td>1,180</td>
                                <td>42.8</td>
                                <td>1.18</td>
                                <td>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 0.75rem;">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Pond B1</strong></td>
                                <td>
                                    <span style="display: inline-flex; align-items: center; padding: 4px 8px; background: #bee3f8; color: #2a4365; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                        <i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 4px;"></i>
                                        Stocking
                                    </span>
                                </td>
                                <td>Vannamei</td>
                                <td>2025-07-25</td>
                                <td>4</td>
                                <td>15.0 pcs/m²</td>
                                <td>0.8</td>
                                <td>98%</td>
                                <td>45</td>
                                <td>2.1</td>
                                <td>0.95</td>
                                <td>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 0.75rem;">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Pond B2</strong></td>
                                <td>
                                    <span style="display: inline-flex; align-items: center; padding: 4px 8px; background: #e9d8fd; color: #553c9a; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                        <i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 4px;"></i>
                                        Renovation
                                    </span>
                                </td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 0.75rem;">
                                        <i class="fas fa-tools"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Pond C1</strong></td>
                                <td>
                                    <span style="display: inline-flex; align-items: center; padding: 4px 8px; background: #fed7d7; color: #c53030; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                        <i class="fas fa-circle" style="font-size: 0.5rem; margin-right: 4px;"></i>
                                        At Risk
                                    </span>
                                </td>
                                <td>Vannamei</td>
                                <td>2025-06-10</td>
                                <td>49</td>
                                <td>11.8 pcs/m²</td>
                                <td>31.2</td>
                                <td>76%</td>
                                <td>980</td>
                                <td>52.4</td>
                                <td>1.68</td>
                                <td>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 0.75rem;">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="content-tabs">
        <button class="content-tab active" onclick="switchTab('active-pond', this)">Active Pond Overview</button>
        <button class="content-tab" onclick="switchTab('farm-report', this)">Farm Report</button>
    </div>

    <!-- Legend -->
    <div class="legend">
        <div class="legend-item">
            <div class="legend-color active-pond"></div>
            <span>Active pond</span>
        </div>
        <div class="legend-item">
            <div class="legend-color stocking-pond"></div>
            <span>Stocking pond</span>
        </div>
        <div class="legend-item">
            <div class="legend-color renovation-pond"></div>
            <span>Renovation pond</span>
        </div>
        <div class="legend-item">
            <div class="legend-color risk-pond-1"></div>
            <div class="legend-color risk-pond-2"></div>
            <div class="legend-color risk-pond-3"></div>
            <a href="/en/risk-management" target="_blank">Risk pond</a>
        </div>
    </div>

    <!-- Tab Content: Active Pond Overview -->
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div>
                            <h3 class="card-title">Production Analytics</h3>
                            <p class="card-subtitle">Growth rates, feed conversion, and yield predictions</p>
                        </div>
                    </div>

                    <div class="interactive-grid">
                        <div class="interactive-item" onclick="showAnalytics('growth')">
                            <span class="interactive-item-number">28.4g</span>
                            <span class="interactive-item-label">Avg Weight</span>
                        </div>
                        <div class="interactive-item" onclick="showAnalytics('fcr')">
                            <span class="interactive-item-number">1.25</span>
                            <span class="interactive-item-label">FCR</span>
                        </div>
                        <div class="interactive-item" onclick="showAnalytics('survival')">
                            <span class="interactive-item-number">89%</span>
                            <span class="interactive-item-label">Survival</span>
                        </div>
                        <div class="interactive-item" onclick="showAnalytics('yield')">
                            <span class="interactive-item-number">2.4K</span>
                            <span class="interactive-item-label">Biomass</span>
                        </div>
                    </div>

                    <div class="data-viz-container">
                        <div style="text-align: center;">
                            <h4 style="color: #2d3748; margin-bottom: 15px;">Weekly Growth Trend</h4>
                            <div style="height: 100px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem;">
                                📈 +12% Growth Rate
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Environmental Monitoring Card -->
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <div>
                            <h3 class="card-title">Environmental Monitoring</h3>
                            <p class="card-subtitle">Water quality parameters and environmental conditions</p>
                        </div>
                    </div>

                    <div class="interactive-grid">
                        <div class="interactive-item" onclick="showEnvironmental('temperature')">
                            <span class="interactive-item-number">28°C</span>
                            <span class="interactive-item-label">Temperature</span>
                        </div>
                        <div class="interactive-item" onclick="showEnvironmental('ph')">
                            <span class="interactive-item-number">7.8</span>
                            <span class="interactive-item-label">pH Level</span>
                        </div>
                        <div class="interactive-item" onclick="showEnvironmental('oxygen')">
                            <span class="interactive-item-number">6.2</span>
                            <span class="interactive-item-label">DO (mg/L)</span>
                        </div>
                        <div class="interactive-item" onclick="showEnvironmental('salinity')">
                            <span class="interactive-item-number">15‰</span>
                            <span class="interactive-item-label">Salinity</span>
                        </div>
                    </div>

                    <div class="data-viz-container">
                        <div style="text-align: center;">
                            <h4 style="color: #2d3748; margin-bottom: 15px;">Water Quality Status</h4>
                            <div style="display: flex; justify-content: space-around; align-items: center;">
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: #48bb78; margin: 0 auto 5px; display: flex; align-items: center; justify-content: center; color: white;">✓</div>
                                    <small style="color: #718096;">Optimal</small>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: #ed8936; margin: 0 auto 5px; display: flex; align-items: center; justify-content: center; color: white;">!</div>
                                    <small style="color: #718096;">Monitor</small>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: #e53e3e; margin: 0 auto 5px; display: flex; align-items: center; justify-content: center; color: white;">⚠</div>
                                    <small style="color: #718096;">Alert</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Farm Operations Card -->
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div>
                            <h3 class="card-title">Farm Operations</h3>
                            <p class="card-subtitle">Daily operations, feeding schedules, and maintenance</p>
                        </div>
                    </div>

                    <div class="interactive-grid">
                        <div class="interactive-item" onclick="showOperations('feeding')">
                            <span class="interactive-item-number">45kg</span>
                            <span class="interactive-item-label">Daily Feed</span>
                        </div>
                        <div class="interactive-item" onclick="showOperations('maintenance')">
                            <span class="interactive-item-number">3</span>
                            <span class="interactive-item-label">Pending Tasks</span>
                        </div>
                        <div class="interactive-item" onclick="showOperations('harvest')">
                            <span class="interactive-item-number">12</span>
                            <span class="interactive-item-label">Days to Harvest</span>
                        </div>
                        <div class="interactive-item" onclick="showOperations('staff')">
                            <span class="interactive-item-number">8</span>
                            <span class="interactive-item-label">Active Staff</span>
                        </div>
                    </div>

                    <div class="data-viz-container">
                        <div style="text-align: center;">
                            <h4 style="color: #2d3748; margin-bottom: 15px;">Today's Schedule</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <div style="background: #e6fffa; padding: 10px; border-radius: 8px; border-left: 4px solid #38b2ac;">
                                    <strong style="color: #2d3748;">06:00</strong> - Morning Feed (Pond A1-A4)
                                </div>
                                <div style="background: #fef5e7; padding: 10px; border-radius: 8px; border-left: 4px solid #ed8936;">
                                    <strong style="color: #2d3748;">14:00</strong> - Water Quality Check
                                </div>
                                <div style="background: #e6fffa; padding: 10px; border-radius: 8px; border-left: 4px solid #38b2ac;">
                                    <strong style="color: #2d3748;">18:00</strong> - Evening Feed (All Ponds)
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Floating Action Buttons -->
            <div class="fab-container">
                <button class="fab" onclick="showQuickActions()" title="Quick Actions">
                    <i class="fas fa-bolt"></i>
                </button>
                <button class="fab" onclick="showNotifications()" title="Notifications">
                    <i class="fas fa-bell"></i>
                </button>
                <button class="fab primary" onclick="showMainMenu()" title="Main Menu">
                    <i class="fas fa-plus"></i>
                </button>
            </div>

            <!-- Tabs -->
            <div class="content-tabs">
                <button class="content-tab active" onclick="switchTab('active-pond', this)">Active Pond Overview</button>
                <button class="content-tab" onclick="switchTab('farm-report', this)">Farm Report</button>
            </div>

            <!-- Legend -->
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color active-pond"></div>
                    <span>Active pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color stocking-pond"></div>
                    <span>Stocking pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color renovation-pond"></div>
                    <span>Renovation pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color risk-pond-1"></div>
                    <div class="legend-color risk-pond-2"></div>
                    <div class="legend-color risk-pond-3"></div>
                    <a href="/en/risk-management" target="_blank">Risk pond</a>
                </div>
            </div>




            <!-- Tab Content: Active Pond Overview -->
            <div id="active-pond-content" class="tab-content">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Header Section -->
                    <div class="mb-8">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                            <div>
                                <h1 class="text-4xl font-bold text-gray-900 font-heading">AquaPond Analytics</h1>
                                <p class="text-gray-700 mt-2">Real-time monitoring and management of aquaculture ponds</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center text-sm text-gray-700 bg-white px-4 py-2 rounded-xl card-shadow">
                                    <i class="fas fa-sync-alt mr-2 text-primary-600"></i>
                                    <span>Last updated: July 29, 2025, 1:54 PM IST</span>
                                </div>
                                <button class="bg-gradient-to-r from-primary-600 to-aqua-600 hover:from-primary-700 hover:to-aqua-700 text-white px-5 py-3 rounded-xl font-medium transition duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-download mr-2"></i> Export Report
                                </button>
                            </div>
                        </div>
                        <!-- Breadcrumbs -->
                        <nav class="flex text-sm mb-6" aria-label="Breadcrumb">
                            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                                <li class="inline-flex items-center">
                                    <a href="#" class="inline-flex items-center text-gray-600 hover:text-primary-600">
                                        <i class="fas fa-home mr-2"></i>
                                        Home
                                    </a>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="#" class="ml-1 text-gray-600 hover:text-primary-600 md:ml-2">Reports</a>
                                    </div>
                                </li>
                                <li aria-current="page">
                                    <div class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="ml-1 text-gray-800 md:ml-2 font-medium">Active Ponds</span>
                                    </div>
                                </li>
                            </ol>
                        </nav>
                    </div>
                    <!-- Filters Bar -->
                    <div class="mb-8 bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label for="farmSelect" class="block text-sm font-medium text-gray-700 mb-2">Farm Location</label>
                                <select id="farmSelect" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <option>Sikindar Shaik Farm</option>
                                    <option>AquaPrime Holdings</option>
                                    <option>Coastal Fisheries</option>
                                    <option>Oceanic Aquaculture</option>
                                </select>
                            </div>
                            <div>
                                <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                                <input type="date" id="date" value="2025-07-29" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                            </div>
                            <div>
                                <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">Status Filter</label>
                                <select id="statusFilter" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <option>All Statuses</option>
                                    <option>Active</option>
                                    <option>Stocking</option>
                                    <option>Renovation</option>
                                    <option>At Risk</option>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button class="w-full bg-gradient-to-r from-primary-600 to-aqua-600 hover:from-primary-700 hover:to-aqua-700 text-white font-semibold px-4 py-3 rounded-xl transition duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-filter mr-2"></i> Apply Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Summary Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Active Card -->
                        <div class="bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Active Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">8</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-water text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>12% from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- Stocking Card -->
                        <div class="bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Stocking Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">4</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-fish text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-down mr-1"></i>
                                    <span>3% from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- Renovation Card -->
                        <div class="bg-gradient-to-br from-purple-400 to-fuchsia-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Renovation Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">2</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-tools text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-equals mr-1"></i>
                                    <span>No change from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- At Risk Card -->
                        <div class="bg-gradient-to-br from-red-400 to-orange-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Ponds At Risk</p>
                                    <p class="text-4xl font-bold text-white mt-1">1</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-down mr-1"></i>
                                    <span>50% from last month</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Main Content - Table -->
                    <div class="bg-white rounded-2xl card-shadow overflow-hidden border border-gray-100">
                        <div class="px-6 py-5 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 font-heading">Pond Management Overview</h2>
                                <p class="text-gray-600 mt-1">Detailed information on all aquaculture ponds</p>
                            </div>
                            <div class="mt-4 md:mt-0 flex space-x-3">
                                <div class="relative">
                                    <input type="text" placeholder="Search ponds..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                                </div>
                                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-xl font-medium transition duration-200">
                                    <i class="fas fa-cog mr-1"></i> Settings
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="table-header">
                                    <tr>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Module</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Pond #</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Status</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Shrimp Type</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Stocking Date</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Age (Days)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Density</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">ABW (g)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Survival %</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Biomass (kg)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Feed/Day (kg)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">FCR</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Row 1 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module A</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 1</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-06-15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">44</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">12.5 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">28.4 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 89%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">89%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1,250 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">45.2 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.25</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Row 2 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module B</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 2</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-06-20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">39</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.8 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">26.7 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 92%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">92%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1,180 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">42.8 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.22</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Row 3 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 3</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-stocking">
                                                <i class="fas fa-seedling mr-1"></i> Stocking
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-07-28</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">15.0 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">0.8 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full bg-gray-300" style="width: 0%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">-</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex flex-col md:flex-row items-center justify-between">
                            <div class="text-sm text-gray-700 mb-4 md:mb-0">
                                Showing <span class="font-bold">1</span> to <span class="font-bold">5</span> of <span class="font-bold">8</span> results
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 text-sm font-bold text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition">
                                    Previous
                                </button>
                                <button class="px-4 py-2 text-sm font-bold text-white bg-gradient-to-r from-primary-600 to-aqua-600 rounded-xl hover:from-primary-700 hover:to-aqua-700 transition">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Analytics Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Production Analytics -->
                        <div class="bg-gradient-to-br from-primary-50 to-aqua-50 rounded-2xl card-shadow p-6 lg:col-span-2 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Production Analytics</h3>
                            <div class="h-64 bg-gradient-to-r from-primary-100 to-aqua-100 rounded-xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-line text-4xl text-primary-500 mb-3 floating"></i>
                                    <p class="text-gray-600">Real-time pond performance analytics</p>
                                    <p class="text-sm text-gray-500 mt-2">Growth rates, feed efficiency, and yield predictions</p>
                                </div>
                            </div>
                        </div>
                        <!-- Management Tools -->
                        <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Management Tools</h3>
                            <div class="space-y-4">
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-primary-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-plus text-primary-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Add New Pond</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-aqua-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-sync-alt text-aqua-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Refresh Data</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-file-export text-purple-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Export Report</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-green-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-bell text-green-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Set Alerts</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Key Performance Indicators -->
                    <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Key Performance Indicators</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-fish text-blue-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. Survival Rate</h4>
                                <p class="text-3xl font-bold text-blue-600 mt-1">89.2%</p>
                                <p class="text-sm text-gray-500">Across active ponds</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-weight text-emerald-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. FCR</h4>
                                <p class="text-3xl font-bold text-emerald-600 mt-1">1.24</p>
                                <p class="text-sm text-gray-500">Feed conversion ratio</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Growth Rate</h4>
                                <p class="text-3xl font-bold text-purple-600 mt-1">0.29</p>
                                <p class="text-sm text-gray-500">g/day average</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-tachometer-alt text-orange-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Efficiency</h4>
                                <p class="text-3xl font-bold text-orange-600 mt-1">92.5%</p>
                                <p class="text-sm text-gray-500">Overall performance</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Content: Farm Report -->
            <div id="farm-report-content" class="tab-content" style="display: none;">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Header Section -->
                    <div class="mb-8">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                            <div>
                                <h1 class="text-4xl font-bold text-gray-900 font-heading">Farm Management Dashboard</h1>
                                <p class="text-gray-700 mt-2">Comprehensive farm operations and production analytics</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center text-sm text-gray-700 bg-white px-4 py-2 rounded-xl card-shadow">
                                    <i class="fas fa-calendar-alt mr-2 text-emerald-600"></i>
                                    <span>Season: 2025 Monsoon</span>
                                </div>
                                <button class="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-5 py-3 rounded-xl font-medium transition duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-file-excel mr-2"></i> Export Data
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Farm Overview Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Total Ponds Card -->
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Total Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">15</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-water text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-chart-line mr-1"></i>
                                    <span>Across 3 modules</span>
                                </div>
                            </div>
                        </div>
                        <!-- Total Area Card -->
                        <div class="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Total Area</p>
                                    <p class="text-4xl font-bold text-white mt-1">45.2</p>
                                    <p class="text-xs text-white opacity-80">Hectares</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-expand-arrows-alt text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>5% expansion planned</span>
                                </div>
                            </div>
                        </div>
                        <!-- Production Capacity Card -->
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Production</p>
                                    <p class="text-4xl font-bold text-white mt-1">125</p>
                                    <p class="text-xs text-white opacity-80">Tons/Season</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-fish text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-target mr-1"></i>
                                    <span>Target: 150 tons</span>
                                </div>
                            </div>
                        </div>
                        <!-- Efficiency Card -->
                        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Efficiency</p>
                                    <p class="text-4xl font-bold text-white mt-1">83%</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-tachometer-alt text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>8% from last season</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Farm Production Table -->
                    <div class="bg-white rounded-2xl card-shadow overflow-hidden border border-gray-100 mb-8">
                        <div class="px-6 py-5 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 font-heading">Farm Production Overview</h2>
                                <p class="text-gray-600 mt-1">Detailed production data across all farm modules</p>
                            </div>
                            <div class="mt-4 md:mt-0 flex space-x-3">
                                <div class="relative">
                                    <input type="text" placeholder="Search modules..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition">
                                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                                </div>
                                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-xl font-medium transition duration-200">
                                    <i class="fas fa-filter mr-1"></i> Filter
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="table-header">
                                    <tr>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Module</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Pond Count</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Total Area (Ha)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Season</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Stock Density</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">PL Origin</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Expected Yield</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Current Status</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Progress</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Module A Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module A</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">5 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">15.2 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-blue-100 text-blue-800">
                                                Monsoon 2025
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">12.5 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Coastal Hatchery</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">45.6 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 75%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">75%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Module B Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module B</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">6 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">18.5 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-blue-100 text-blue-800">
                                                Monsoon 2025
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.8 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Premium Aqua</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">52.3 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 68%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">68%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Module C Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">4 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.5 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-yellow-100 text-yellow-800">
                                                Preparation
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">13.2 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Marine Genetics</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">38.1 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-stocking">
                                                <i class="fas fa-seedling mr-1"></i> Preparing
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full bg-yellow-500" style="width: 25%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">25%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex flex-col md:flex-row items-center justify-between">
                            <div class="text-sm text-gray-700 mb-4 md:mb-0">
                                Showing <span class="font-bold">1</span> to <span class="font-bold">3</span> of <span class="font-bold">3</span> modules
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 text-sm font-bold text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition">
                                    Previous
                                </button>
                                <button class="px-4 py-2 text-sm font-bold text-white bg-gradient-to-r from-emerald-600 to-teal-600 rounded-xl hover:from-emerald-700 hover:to-teal-700 transition">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Additional Analytics Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Production Analytics -->
                        <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl card-shadow p-6 lg:col-span-2 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Production Analytics</h3>
                            <div class="h-64 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-bar text-4xl text-emerald-500 mb-3 floating"></i>
                                    <p class="text-gray-600">Production analytics chart would appear here</p>
                                    <p class="text-sm text-gray-500 mt-2">Showing yield trends across all modules</p>
                                </div>
                            </div>
                        </div>
                        <!-- Farm Management Tools -->
                        <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Management Tools</h3>
                            <div class="space-y-4">
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-emerald-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-seedling text-emerald-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Plan Stocking</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-teal-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-calendar-check text-teal-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Schedule Harvest</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">View Reports</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-cog text-purple-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Farm Settings</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Key Performance Indicators</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-fish text-blue-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Survival Rate</h4>
                                <p class="text-3xl font-bold text-blue-600 mt-1">87.5%</p>
                                <p class="text-sm text-gray-500">Average across all ponds</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-weight text-emerald-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. FCR</h4>
                                <p class="text-3xl font-bold text-emerald-600 mt-1">1.28</p>
                                <p class="text-sm text-gray-500">Feed conversion ratio</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-clock text-purple-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Cycle Time</h4>
                                <p class="text-3xl font-bold text-purple-600 mt-1">105</p>
                                <p class="text-sm text-gray-500">Days average</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-dollar-sign text-orange-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Profit Margin</h4>
                                <p class="text-3xl font-bold text-orange-600 mt-1">24.3%</p>
                                <p class="text-sm text-gray-500">This season</p>
                            </div>
                        </div>
                    </div>
            </div>

            <!-- Farm List Content -->
            <div id="farm-list-content" class="farm-content" style="display: none;">
                <div class="farm-header">
                    <div class="farm-header-top">
                        <div class="farm-title-section">
                            <h1 class="page-title">Farm list</h1>
                        </div>
                        <div class="farm-actions">
                            <div class="search-container">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" placeholder="Search..." class="search-input">
                            </div>
                            <button class="btn btn-orange">
                                <i class="fas fa-plus-circle"></i>
                                Quick setup farm
                            </button>
                            <button class="btn btn-orange">
                                <i class="fas fa-plus"></i>
                                Add New
                            </button>
                        </div>
                    </div>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">Home</span>
                        <i class="fas fa-chevron-right breadcrumb-separator"></i>
                        <span class="breadcrumb-item active">Farm list</span>
                    </div>
                </div>

                <div class="farm-table-container">
                    <table class="farm-table">
                        <thead>
                            <tr>
                                <th>
                                    <span>Farm code</span>
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th>
                                    <span>Farm name</span>
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th>Address</th>
                                <th>Manager</th>
                                <th>Number of modules</th>
                                <th>Number of ponds</th>
                                <th>Active ponds</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="farm-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    sha
                                </td>
                                <td>puttlacheruvu</td>
                                <td>-</td>
                                <td>Sikindar Shaik</td>
                                <td>0</td>
                                <td>0</td>
                                <td>0</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view-btn" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete-btn" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="farm-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    Sikindar_ShaikSikindar Shaik
                                </td>
                                <td>-</td>
                                <td>Mudinepalle, Eluru, 521325, Andhra Pradesh, India</td>
                                <td>Sikindar Shaik</td>
                                <td>1</td>
                                <td>1</td>
                                <td>0</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view-btn" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete-btn" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination-container">
                    <div class="pagination-info">
                        <span>Number of farms per page</span>
                        <select class="page-size-select">
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setActive(element) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            element.classList.add('active');
        }

        function toggleDropdown(element, dropdownType) {
            const dropdownContainer = element.parentElement;
            const dropdown = dropdownContainer.querySelector('.nav-dropdown');
            
            // Close other dropdowns first
            const allDropdowns = document.querySelectorAll('.nav-item-with-dropdown');
            allDropdowns.forEach(item => {
                if (item !== dropdownContainer) {
                    item.classList.remove('open');
                    item.querySelector('.nav-dropdown').classList.remove('open');
                }
            });
            
            // Toggle current dropdown
            dropdownContainer.classList.toggle('open');
            dropdown.classList.toggle('open');
            
            // Set active state for parent nav item
            setActive(element);
        }

        function setActiveDropdownItem(element) {
            // Remove active class from all dropdown items in this dropdown
            const dropdown = element.closest('.nav-dropdown');
            const dropdownItems = dropdown.querySelectorAll('.nav-dropdown-item');
            dropdownItems.forEach(item => item.classList.remove('active'));
            
            // Add active class to clicked item
            element.classList.add('active');
        }

        // Farm System dropdown functions
        function showFarm() {
            hideAllContent();
            const mainContent = document.querySelector('.main-content');
            
            mainContent.innerHTML = `
                <div class="farm-content">
                    <div style="padding: 24px;">
                        <!-- Breadcrumb Navigation -->
                        <nav class="breadcrumb-nav">
                            <ol>
                                <li><a href="#">Home</a></li>
                                <li><span class="breadcrumb-separator">›</span></li>
                                <li><span style="color: #6b7280;">Farm list</span></li>
                            </ol>
                        </nav>

                        <!-- Page Title -->
                        <h1 style="font-size: 2rem; font-weight: bold; margin-bottom: 1rem; color: #111827;">Farm list</h1>

                        <!-- Search and Actions -->
                        <div class="search-actions-container">
                            <div class="search-container">
                                <input type="text" placeholder="Search..." class="search-input">
                                <button class="search-button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="action-buttons-container">
                                <button class="action-button">
                                    <i class="fas fa-plus"></i> Quick setup farm
                                </button>
                                <button class="action-button">
                                    <i class="fas fa-plus"></i> Add New
                                </button>
                            </div>
                        </div>

                        <!-- Table -->
                        <div class="table-container">
                            <table class="farm-table">
                                <thead class="table-header">
                                    <tr>
                                        <th>
                                            Farm code
                                            <i class="fas fa-sort" style="margin-left: 0.5rem; color: #9ca3af;"></i>
                                        </th>
                                        <th>
                                            Farm name
                                            <i class="fas fa-sort" style="margin-left: 0.5rem; color: #9ca3af;"></i>
                                        </th>
                                        <th>Address</th>
                                        <th>Manager</th>
                                        <th>Number of modules</th>
                                        <th>Number of ponds</th>
                                        <th>Active ponds</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Row 1 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-user" style="color: #6b7280;"></i>
                                                </div>
                                                <span>sha</span>
                                            </div>
                                        </td>
                                        <td>puttlacheruvu</td>
                                        <td>-</td>
                                        <td>Sikindar Shaik</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 2 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-user" style="color: #6b7280;"></i>
                                                </div>
                                                <span>Sikindar_Shaik</span>
                                            </div>
                                        </td>
                                        <td>Sikindar Shaik</td>
                                        <td>Mudinepalle, Eluru, 521325, Andhra Pradesh, India</td>
                                        <td>Sikindar Shaik</td>
                                        <td>1</td>
                                        <td>1</td>
                                        <td>0</td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 3 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-user" style="color: #6b7280;"></i>
                                                </div>
                                                <span>demo_farm</span>
                                            </div>
                                        </td>
                                        <td>Demo Farm</td>
                                        <td>Chennai, Tamil Nadu, India</td>
                                        <td>Farm Manager</td>
                                        <td>2</td>
                                        <td>5</td>
                                        <td>3</td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 4 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-user" style="color: #6b7280;"></i>
                                                </div>
                                                <span>aqua_farm_01</span>
                                            </div>
                                        </td>
                                        <td>Aqua Farm Premium</td>
                                        <td>Vijayawada, Andhra Pradesh, India</td>
                                        <td>Rajesh Kumar</td>
                                        <td>3</td>
                                        <td>8</td>
                                        <td>6</td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 5 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-user" style="color: #6b7280;"></i>
                                                </div>
                                                <span>coastal_farm</span>
                                            </div>
                                        </td>
                                        <td>Coastal Aquaculture</td>
                                        <td>Nellore, Andhra Pradesh, India</td>
                                        <td>Suresh Reddy</td>
                                        <td>4</td>
                                        <td>12</td>
                                        <td>9</td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="pagination-container">
                            <div class="pagination-controls">
                                <span class="pagination-text">Number of farms per page</span>
                                <select class="pagination-select">
                                    <option selected>10</option>
                                    <option>20</option>
                                    <option>50</option>
                                </select>
                                <button class="pagination-button" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="pagination-button">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function showPond() {
            hideAllContent();
            const mainContent = document.querySelector('.main-content');
            
            mainContent.innerHTML = `
                <div class="farm-content">
                    <div style="padding: 24px;">
                        <!-- Breadcrumb Navigation -->
                        <nav class="breadcrumb-nav">
                            <ol>
                                <li><a href="#">Home</a></li>
                                <li><span class="breadcrumb-separator">›</span></li>
                                <li><span style="color: #6b7280;">List of ponds</span></li>
                            </ol>
                        </nav>

                        <!-- Page Title -->
                        <h1 style="font-size: 2rem; font-weight: bold; margin-bottom: 1rem; color: #111827;">List of ponds</h1>

                        <!-- Search and Filters -->
                        <div class="search-actions-container">
                            <div class="search-container">
                                <input type="text" placeholder="Search..." class="search-input">
                                <button class="search-button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="action-buttons-container">
                                <select style="border: 1px solid #d1d5db; padding: 0.5rem; border-radius: 0.5rem; margin-right: 1rem; background: white;">
                                    <option selected>Farm</option>
                                    <option>Sikindar Shaik</option>
                                    <option>Demo Farm</option>
                                    <option>Aqua Farm Premium</option>
                                    <option>Coastal Aquaculture</option>
                                </select>
                                <button class="action-button">
                                    <i class="fas fa-plus"></i> Add New
                                </button>
                            </div>
                        </div>

                        <!-- Table -->
                        <div class="table-container">
                            <table class="farm-table">
                                <thead class="table-header">
                                    <tr>
                                        <th>
                                            Pond code
                                            <i class="fas fa-sort" style="margin-left: 0.5rem; color: #9ca3af;"></i>
                                        </th>
                                        <th>
                                            Pond name
                                            <i class="fas fa-sort" style="margin-left: 0.5rem; color: #9ca3af;"></i>
                                        </th>
                                        <th>Module name</th>
                                        <th>Farm name</th>
                                        <th>Module manager</th>
                                        <th>Pond type</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Row 1 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-water" style="color: #6b7280;"></i>
                                                </div>
                                                <span>01</span>
                                            </div>
                                        </td>
                                        <td>putlacheruvu</td>
                                        <td>A1</td>
                                        <td>Sikindar Shaik</td>
                                        <td>-</td>
                                        <td>Grow-out pond</td>
                                        <td><span style="color: #dc3545; font-weight: 500;">Inactive</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 2 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-water" style="color: #6b7280;"></i>
                                                </div>
                                                <span>02</span>
                                            </div>
                                        </td>
                                        <td>Pond Alpha</td>
                                        <td>A2</td>
                                        <td>Demo Farm</td>
                                        <td>Rajesh Kumar</td>
                                        <td>Nursery pond</td>
                                        <td><span style="color: #28a745; font-weight: 500;">Active</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 3 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-water" style="color: #6b7280;"></i>
                                                </div>
                                                <span>03</span>
                                            </div>
                                        </td>
                                        <td>Pond Beta</td>
                                        <td>B1</td>
                                        <td>Aqua Farm Premium</td>
                                        <td>Suresh Reddy</td>
                                        <td>Grow-out pond</td>
                                        <td><span style="color: #28a745; font-weight: 500;">Active</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 4 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-water" style="color: #6b7280;"></i>
                                                </div>
                                                <span>04</span>
                                            </div>
                                        </td>
                                        <td>Pond Gamma</td>
                                        <td>B2</td>
                                        <td>Coastal Aquaculture</td>
                                        <td>Venkat Rao</td>
                                        <td>Broodstock pond</td>
                                        <td><span style="color: #ffc107; font-weight: 500;">Maintenance</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 5 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-water" style="color: #6b7280;"></i>
                                                </div>
                                                <span>05</span>
                                            </div>
                                        </td>
                                        <td>Pond Delta</td>
                                        <td>C1</td>
                                        <td>Demo Farm</td>
                                        <td>Farm Manager</td>
                                        <td>Grow-out pond</td>
                                        <td><span style="color: #28a745; font-weight: 500;">Active</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 6 -->
                                    <tr class="table-row">
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-water" style="color: #6b7280;"></i>
                                                </div>
                                                <span>06</span>
                                            </div>
                                        </td>
                                        <td>Pond Epsilon</td>
                                        <td>C2</td>
                                        <td>Aqua Farm Premium</td>
                                        <td>Rajesh Kumar</td>
                                        <td>Nursery pond</td>
                                        <td><span style="color: #dc3545; font-weight: 500;">Inactive</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="pagination-container">
                            <div class="pagination-controls">
                                <span class="pagination-text">Number of ponds per page</span>
                                <select class="pagination-select">
                                    <option selected>10</option>
                                    <option>20</option>
                                    <option>50</option>
                                </select>
                                <button class="pagination-button" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="pagination-button">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function showShrimpDiary() {
            hideAllContent();
            const mainContent = document.querySelector('.main-content');
            
            mainContent.innerHTML = `
                <div class="farm-content">
                    <div style="padding: 24px;">
                        <!-- Breadcrumb Navigation -->
                        <nav class="breadcrumb-nav">
                            <ol>
                                <li><a href="#">Home</a></li>
                                <li><span class="breadcrumb-separator">›</span></li>
                                <li><span style="color: #6b7280;">Shrimp Diary</span></li>
                            </ol>
                        </nav>

                        <!-- Page Title -->
                        <h1 style="font-size: 2rem; font-weight: bold; margin-bottom: 1rem; color: #111827;">Shrimp Diary</h1>

                        <!-- Search and Filters -->
                        <div class="search-actions-container">
                            <div class="search-container">
                                <input type="text" placeholder="Search entries..." class="search-input">
                                <button class="search-button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="action-buttons-container">
                                <select style="border: 1px solid #d1d5db; padding: 0.5rem; border-radius: 0.5rem; margin-right: 1rem; background: white;">
                                    <option selected>All Ponds</option>
                                    <option>Pond 01</option>
                                    <option>Pond 02</option>
                                    <option>Pond 03</option>
                                    <option>Pond 04</option>
                                </select>
                                <select style="border: 1px solid #d1d5db; padding: 0.5rem; border-radius: 0.5rem; margin-right: 1rem; background: white;">
                                    <option selected>This Month</option>
                                    <option>Last 7 Days</option>
                                    <option>Last 30 Days</option>
                                    <option>Last 3 Months</option>
                                    <option>Custom Range</option>
                                </select>
                                <button class="action-button">
                                    <i class="fas fa-plus"></i> Add Entry
                                </button>
                            </div>
                        </div>

                        <!-- Table -->
                        <div class="table-container" style="overflow-x: auto; border: 1px solid #e2e8f0; border-radius: 0.5rem; background: white;">
                            <table class="farm-table" style="width: 100%; border-collapse: collapse; min-width: 1200px;">
                                <thead class="table-header">
                                    <tr>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">
                                            Date
                                            <i class="fas fa-sort" style="margin-left: 0.5rem; color: #9ca3af;"></i>
                                        </th>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">
                                            Pond
                                            <i class="fas fa-sort" style="margin-left: 0.5rem; color: #9ca3af;"></i>
                                        </th>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">DOC (Days)</th>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">Feed Quantity (kg)</th>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">Water Quality</th>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">Growth Rate</th>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">Mortality</th>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">Remarks</th>
                                        <th style="padding: 0.75rem; text-align: left; font-size: 0.875rem; font-weight: 500; color: #111827; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Row 1 -->
                                    <tr class="table-row" style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div style="display: flex; flex-direction: column;">
                                                <span style="font-weight: 500; color: #111827;">Aug 04, 2025</span>
                                                <span style="font-size: 0.75rem; color: #9ca3af;">08:30 AM</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-fish" style="color: #6b7280;"></i>
                                                </div>
                                                <span>Pond 01</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">45</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">25.5</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <span style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="width: 8px; height: 8px; background: #22c55e; border-radius: 50%;"></span>
                                                Good
                                            </span>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">12.5g</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;"><span style="color: #dc3545;">2.1%</span></td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">Normal feeding response</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 2 -->
                                    <tr class="table-row" style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div style="display: flex; flex-direction: column;">
                                                <span style="font-weight: 500; color: #111827;">Aug 03, 2025</span>
                                                <span style="font-size: 0.75rem; color: #9ca3af;">07:45 AM</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-fish" style="color: #6b7280;"></i>
                                                </div>
                                                <span>Pond 02</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">38</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">22.0</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <span style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="width: 8px; height: 8px; background: #eab308; border-radius: 50%;"></span>
                                                Average
                                            </span>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">10.8g</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;"><span style="color: #22c55e;">1.5%</span></td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">Water exchange required</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 3 -->
                                    <tr class="table-row" style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div style="display: flex; flex-direction: column;">
                                                <span style="font-weight: 500; color: #111827;">Aug 02, 2025</span>
                                                <span style="font-size: 0.75rem; color: #9ca3af;">09:15 AM</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-fish" style="color: #6b7280;"></i>
                                                </div>
                                                <span>Pond 03</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">52</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">30.2</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <span style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="width: 8px; height: 8px; background: #22c55e; border-radius: 50%;"></span>
                                                Good
                                            </span>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">15.2g</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;"><span style="color: #22c55e;">1.8%</span></td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">Excellent growth observed</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 4 -->
                                    <tr class="table-row" style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div style="display: flex; flex-direction: column;">
                                                <span style="font-weight: 500; color: #111827;">Aug 01, 2025</span>
                                                <span style="font-size: 0.75rem; color: #9ca3af;">08:00 AM</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-fish" style="color: #6b7280;"></i>
                                                </div>
                                                <span>Pond 04</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">29</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">18.5</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <span style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="width: 8px; height: 8px; background: #ef4444; border-radius: 50%;"></span>
                                                Poor
                                            </span>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">8.9g</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;"><span style="color: #dc3545;">3.2%</span></td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">Disease treatment applied</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 5 -->
                                    <tr class="table-row" style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div style="display: flex; flex-direction: column;">
                                                <span style="font-weight: 500; color: #111827;">Jul 31, 2025</span>
                                                <span style="font-size: 0.75rem; color: #9ca3af;">07:30 AM</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-fish" style="color: #6b7280;"></i>
                                                </div>
                                                <span>Pond 01</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">44</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">24.8</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <span style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="width: 8px; height: 8px; background: #22c55e; border-radius: 50%;"></span>
                                                Good
                                            </span>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">12.1g</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;"><span style="color: #22c55e;">1.9%</span></td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">Regular monitoring</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Row 6 -->
                                    <tr class="table-row" style="border-bottom: 1px solid #e5e7eb;">
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div style="display: flex; flex-direction: column;">
                                                <span style="font-weight: 500; color: #111827;">Jul 30, 2025</span>
                                                <span style="font-size: 0.75rem; color: #9ca3af;">08:45 AM</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <i class="fas fa-fish" style="color: #6b7280;"></i>
                                                </div>
                                                <span>Pond 02</span>
                                            </div>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">37</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">21.5</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <span style="display: flex; align-items: center; gap: 0.5rem;">
                                                <span style="width: 8px; height: 8px; background: #eab308; border-radius: 50%;"></span>
                                                Average
                                            </span>
                                        </td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">10.5g</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;"><span style="color: #22c55e;">1.7%</span></td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">Feed adjustment made</td>
                                        <td style="padding: 0.75rem; white-space: nowrap; font-size: 0.875rem; color: #6b7280;">
                                            <div class="table-actions">
                                                <button class="action-icon" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="action-icon" title="Edit">
                                                    <i class="fas fa-pencil-alt"></i>
                                                </button>
                                                <button class="action-icon" title="Delete">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary Cards -->
                        <div style="margin-top: 2rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
                                <div style="display: flex; items-center; justify-content: space-between;">
                                    <div>
                                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Average Growth Rate</p>
                                        <p style="font-size: 1.5rem; font-weight: bold; color: #111827; margin: 0.5rem 0 0 0;">11.8g</p>
                                    </div>
                                    <div style="width: 3rem; height: 3rem; background: #dbeafe; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-chart-line" style="color: #3b82f6; font-size: 1.25rem;"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
                                <div style="display: flex; items-center; justify-content: space-between;">
                                    <div>
                                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Average Mortality</p>
                                        <p style="font-size: 1.5rem; font-weight: bold; color: #111827; margin: 0.5rem 0 0 0;">2.0%</p>
                                    </div>
                                    <div style="width: 3rem; height: 3rem; background: #fef3c7; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b; font-size: 1.25rem;"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
                                <div style="display: flex; items-center; justify-content: space-between;">
                                    <div>
                                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Total Feed Used</p>
                                        <p style="font-size: 1.5rem; font-weight: bold; color: #111827; margin: 0.5rem 0 0 0;">142.5kg</p>
                                    </div>
                                    <div style="width: 3rem; height: 3rem; background: #dcfce7; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-seedling" style="color: #22c55e; font-size: 1.25rem;"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; border: 1px solid #e5e7eb;">
                                <div style="display: flex; items-center; justify-content: space-between;">
                                    <div>
                                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Active Ponds</p>
                                        <p style="font-size: 1.5rem; font-weight: bold; color: #111827; margin: 0.5rem 0 0 0;">4/4</p>
                                    </div>
                                    <div style="width: 3rem; height: 3rem; background: #f0f9ff; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-water" style="color: #0ea5e9; font-size: 1.25rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="pagination-container">
                            <div class="pagination-controls">
                                <span class="pagination-text">Number of entries per page</span>
                                <select class="pagination-select">
                                    <option selected>10</option>
                                    <option>20</option>
                                    <option>50</option>
                                </select>
                                <button class="pagination-button" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="pagination-button">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Helper function to hide all content sections
        function hideAllContent() {
            const dashboardContent = document.getElementById('dashboard-content');
            const farmListContent = document.getElementById('farm-list-content');
            const activePondContent = document.getElementById('active-pond-content');
            const farmReportContent = document.getElementById('farm-report-content');
            const tabContents = document.querySelectorAll('.tab-content');
            const dashboardSection = document.querySelector('.dashboard-section');
            
            if (dashboardContent) {
                dashboardContent.style.display = 'none !important';
                console.log('Hid dashboard content');
            }
            if (dashboardSection) {
                dashboardSection.style.display = 'none !important';
                console.log('Hid dashboard section');
            }
            if (farmListContent) farmListContent.style.display = 'none';
            if (activePondContent) activePondContent.style.display = 'none';
            if (farmReportContent) farmReportContent.style.display = 'none';
            
            // Hide all tab contents
            tabContents.forEach(content => {
                content.style.display = 'none';
            });
        }

        // Payment dropdown functions
        function showMine() {
            hideAllContent();
            const mainContent = document.querySelector('.main-content');
            
            mainContent.innerHTML = `
                <div class="farm-content">
                    <div style="padding: 24px; font-family: 'Inter', sans-serif;">
                        <!-- Breadcrumb Navigation -->
                        <nav class="breadcrumb-nav">
                            <ol>
                                <li><a href="#">Home</a></li>
                                <li><span class="breadcrumb-separator">›</span></li>
                                <li><span style="color: #6b7280;">User's Detail</span></li>
                            </ol>
                        </nav>

                        <!-- User Details Card -->
                        <div style="background: white; border-radius: 0.5rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); padding: 1.5rem; margin-bottom: 1rem;">
                            <h2 style="font-size: 1.25rem; font-weight: bold; margin-bottom: 1rem; color: #111827;">User's detail</h2>

                            <!-- User Information Grid -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div>
                                    <p style="font-weight: 500; color: #374151; margin-bottom: 0.25rem;">Account (*)</p>
                                    <p style="font-size: 1.125rem; font-weight: bold; color: #111827;">SIKINDAR SHAIK</p>
                                </div>
                                <div>
                                    <p style="font-weight: 500; color: #374151; margin-bottom: 0.25rem;">Point balance</p>
                                    <p style="font-size: 1.5rem; font-weight: bold; color: #f97316;">0</p>
                                </div>
                                <div>
                                    <p style="font-weight: 500; color: #374151; margin-bottom: 0.25rem;">Email</p>
                                    <p style="color: #6b7280;"><EMAIL></p>
                                </div>
                                <div></div>
                                <div>
                                    <p style="font-weight: 500; color: #374151; margin-bottom: 0.25rem;">Phone</p>
                                    <p style="color: #6b7280;">N/A</p>
                                </div>
                                <div></div>
                                <div>
                                    <p style="font-weight: 500; color: #374151; margin-bottom: 0.25rem;">Management level</p>
                                    <p style="color: #6b7280;">Main account</p>
                                </div>
                                <div></div>
                                <div>
                                    <p style="font-weight: 500; color: #374151; margin-bottom: 0.25rem;">Address (*)</p>
                                    <p style="color: #6b7280;">N/A</p>
                                </div>
                                <div></div>
                            </div>
                        </div>

                        <!-- Tabs and Content Card -->
                        <div style="background: white; border-radius: 0.5rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); padding: 1.5rem;">
                            <!-- Tabs Navigation -->
                            <nav style="display: flex; gap: 1rem; border-bottom: 1px solid #e2e8f0; margin-bottom: 1.5rem;">
                                <a href="#" onclick="switchTab('top-up')" id="tab-top-up" style="padding: 0.75rem 1.25rem; cursor: pointer; transition: all 0.2s ease; color: #0ea5e9; border-bottom: 2px solid #0ea5e9; font-weight: 500; text-decoration: none;">Top-up</a>
                                <a href="#" onclick="switchTab('active-product')" id="tab-active-product" style="padding: 0.75rem 1.25rem; cursor: pointer; transition: all 0.2s ease; color: #6b7280; border-bottom: 2px solid transparent; text-decoration: none;">Active product</a>
                                <a href="#" onclick="switchTab('transaction-history')" id="tab-transaction-history" style="padding: 0.75rem 1.25rem; cursor: pointer; transition: all 0.2s ease; color: #6b7280; border-bottom: 2px solid transparent; text-decoration: none;">Transaction history</a>
                                <a href="#" onclick="switchTab('point-used-history')" id="tab-point-used-history" style="padding: 0.75rem 1.25rem; cursor: pointer; transition: all 0.2s ease; color: #6b7280; border-bottom: 2px solid transparent; text-decoration: none;">Point used history</a>
                                <a href="#" onclick="switchTab('point-used-statistics')" id="tab-point-used-statistics" style="padding: 0.75rem 1.25rem; cursor: pointer; transition: all 0.2s ease; color: #6b7280; border-bottom: 2px solid transparent; text-decoration: none;">Point used statistics</a>
                            </nav>

                            <!-- Tab Content -->
                            <div id="content-top-up" class="tab-content-mine">
                                <label for="recharge-code" style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Nhập mã thẻ nạp điểm</label>
                                <div style="margin-bottom: 1rem;">
                                    <input type="text" id="recharge-code" name="recharge-code" 
                                           style="box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); border: 1px solid #d1d5db; border-radius: 0.375rem; padding: 0.5rem 0.75rem; width: 100%; font-size: 0.875rem; focus:ring-2 focus:ring-blue-500 focus:border-blue-500;"
                                           placeholder="Enter recharge code here">
                                </div>
                                <button onclick="submitTopUp()" 
                                        style="background-color: #f97316; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 500; cursor: pointer; transition: all 0.2s ease;"
                                        onmouseover="this.style.backgroundColor='#ea580c'" 
                                        onmouseout="this.style.backgroundColor='#f97316'">
                                    Top-up
                                </button>
                            </div>

                            <div id="content-active-product" class="tab-content-mine" style="display: none;">
                                <div style="text-align: center; padding: 2rem;">
                                    <i class="fas fa-box-open" style="font-size: 3rem; color: #d1d5db; margin-bottom: 1rem;"></i>
                                    <h3 style="font-size: 1.125rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">No Active Products</h3>
                                    <p style="color: #6b7280;">You currently have no active products.</p>
                                </div>
                            </div>

                            <div id="content-transaction-history" class="tab-content-mine" style="display: none;">
                                <div style="text-align: center; padding: 2rem;">
                                    <i class="fas fa-history" style="font-size: 3rem; color: #d1d5db; margin-bottom: 1rem;"></i>
                                    <h3 style="font-size: 1.125rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">No Transaction History</h3>
                                    <p style="color: #6b7280;">No transactions have been made yet.</p>
                                </div>
                            </div>

                            <div id="content-point-used-history" class="tab-content-mine" style="display: none;">
                                <div style="text-align: center; padding: 2rem;">
                                    <i class="fas fa-coins" style="font-size: 3rem; color: #d1d5db; margin-bottom: 1rem;"></i>
                                    <h3 style="font-size: 1.125rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">No Point Usage History</h3>
                                    <p style="color: #6b7280;">No points have been used yet.</p>
                                </div>
                            </div>

                            <div id="content-point-used-statistics" class="tab-content-mine" style="display: none;">
                                <div style="text-align: center; padding: 2rem;">
                                    <i class="fas fa-chart-bar" style="font-size: 3rem; color: #d1d5db; margin-bottom: 1rem;"></i>
                                    <h3 style="font-size: 1.125rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">No Statistics Available</h3>
                                    <p style="color: #6b7280;">Point usage statistics will appear here once you start using points.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Tab switching function for Mine page
        function switchTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content-mine');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });

            // Remove active style from all tabs
            const tabs = document.querySelectorAll('[id^="tab-"]');
            tabs.forEach(tab => {
                tab.style.color = '#6b7280';
                tab.style.borderBottom = '2px solid transparent';
                tab.style.fontWeight = 'normal';
            });

            // Show selected tab content
            const selectedContent = document.getElementById(`content-${tabName}`);
            if (selectedContent) {
                selectedContent.style.display = 'block';
            }

            // Add active style to selected tab
            const selectedTab = document.getElementById(`tab-${tabName}`);
            if (selectedTab) {
                selectedTab.style.color = '#0ea5e9';
                selectedTab.style.borderBottom = '2px solid #0ea5e9';
                selectedTab.style.fontWeight = '500';
            }
        }

        // Top-up form submission
        function submitTopUp() {
            const rechargeCode = document.getElementById('recharge-code').value;
            if (!rechargeCode.trim()) {
                alert('Please enter a recharge code');
                return;
            }
            
            // Here you would typically send the recharge code to your backend
            alert(`Top-up submitted with code: ${rechargeCode}`);
            
            // Clear the input field
            document.getElementById('recharge-code').value = '';
        }

        function showStaff() {
            hideAllContent();
            const mainContent = document.querySelector('.main-content');
            
            mainContent.innerHTML = `
                <div class="farm-content">
                    <div style="padding: 24px; font-family: 'Inter', sans-serif;">
                        <!-- Breadcrumb Navigation -->
                        <nav class="breadcrumb-nav">
                            <ol>
                                <li><a href="#">Home</a></li>
                                <li><span class="breadcrumb-separator">›</span></li>
                                <li><a href="#" style="color: #0ea5e9;">Payment</a></li>
                                <li><span class="breadcrumb-separator">›</span></li>
                                <li><span style="color: #6b7280;">Staff Payments</span></li>
                            </ol>
                        </nav>

                        <!-- Page Title and Actions -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                            <h1 style="font-size: 2rem; font-weight: bold; color: #111827; margin: 0;">Staff Payments</h1>
                            <div style="display: flex; gap: 1rem;">
                                <button onclick="openPaymentModal()" style="background-color: #0ea5e9; color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='#0284c7'" onmouseout="this.style.backgroundColor='#0ea5e9'">
                                    <i class="fas fa-plus"></i> Process Payment
                                </button>
                                <button onclick="exportPaymentData()" style="background-color: #10b981; color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='#059669'" onmouseout="this.style.backgroundColor='#10b981'">
                                    <i class="fas fa-download"></i> Export Payments
                                </button>
                            </div>
                        </div>

                        <!-- Payment Stats Cards -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                            <div style="background: white; padding: 1.5rem; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); border-left: 4px solid #0ea5e9;">
                                <div style="display: flex; items-center; justify-content: space-between;">
                                    <div>
                                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Total Payroll</p>
                                        <p style="font-size: 2rem; font-weight: bold; color: #111827; margin: 0.5rem 0 0 0;">₹2,45,680</p>
                                    </div>
                                    <div style="width: 3rem; height: 3rem; background: #dbeafe; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-rupee-sign" style="color: #0ea5e9; font-size: 1.25rem;"></i>
                                    </div>
                                </div>
                            </div>

                            <div style="background: white; padding: 1.5rem; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); border-left: 4px solid #10b981;">
                                <div style="display: flex; items-center; justify-content: space-between;">
                                    <div>
                                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Paid This Month</p>
                                        <p style="font-size: 2rem; font-weight: bold; color: #111827; margin: 0.5rem 0 0 0;">₹1,89,450</p>
                                    </div>
                                    <div style="width: 3rem; height: 3rem; background: #dcfce7; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-check-circle" style="color: #10b981; font-size: 1.25rem;"></i>
                                    </div>
                                </div>
                            </div>

                            <div style="background: white; padding: 1.5rem; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); border-left: 4px solid #f59e0b;">
                                <div style="display: flex; items-center; justify-content: space-between;">
                                    <div>
                                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Pending Payments</p>
                                        <p style="font-size: 2rem; font-weight: bold; color: #111827; margin: 0.5rem 0 0 0;">₹56,230</p>
                                    </div>
                                    <div style="width: 3rem; height: 3rem; background: #fef3c7; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-clock" style="color: #f59e0b; font-size: 1.25rem;"></i>
                                    </div>
                                </div>
                            </div>

                            <div style="background: white; padding: 1.5rem; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); border-left: 4px solid #ef4444;">
                                <div style="display: flex; items-center; justify-content: space-between;">
                                    <div>
                                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Overdue</p>
                                        <p style="font-size: 2rem; font-weight: bold; color: #111827; margin: 0.5rem 0 0 0;">₹12,800</p>
                                    </div>
                                    <div style="width: 3rem; height: 3rem; background: #fee2e2; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-exclamation-triangle" style="color: #ef4444; font-size: 1.25rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Filters and Search -->
                        <div style="background: white; padding: 1.5rem; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); margin-bottom: 1.5rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; gap: 1rem; flex-wrap: wrap;">
                                <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                                    <div style="position: relative;">
                                        <input type="text" id="payment-search" placeholder="Search staff payments..." style="border: 1px solid #d1d5db; border-radius: 0.5rem; padding: 0.5rem 1rem; padding-left: 2.5rem; width: 300px; font-size: 0.875rem; transition: border-color 0.2s;" onfocus="this.style.borderColor='#0ea5e9'; this.style.boxShadow='0 0 0 3px rgba(14, 165, 233, 0.1)'" onblur="this.style.borderColor='#d1d5db'; this.style.boxShadow='none'">
                                        <i class="fas fa-search" style="position: absolute; left: 0.75rem; top: 50%; transform: translateY(-50%); color: #6b7280;"></i>
                                    </div>
                                    
                                    <select style="border: 1px solid #d1d5db; border-radius: 0.5rem; padding: 0.5rem 1rem; background: white; font-size: 0.875rem;">
                                        <option>All Departments</option>
                                        <option>Farm Operations</option>
                                        <option>Pond Management</option>
                                        <option>Quality Control</option>
                                        <option>Maintenance</option>
                                        <option>Administration</option>
                                    </select>

                                    <select style="border: 1px solid #d1d5db; border-radius: 0.5rem; padding: 0.5rem 1rem; background: white; font-size: 0.875rem;">
                                        <option>Payment Status</option>
                                        <option>Paid</option>
                                        <option>Pending</option>
                                        <option>Overdue</option>
                                        <option>Processing</option>
                                    </select>

                                    <select style="border: 1px solid #d1d5db; border-radius: 0.5rem; padding: 0.5rem 1rem; background: white; font-size: 0.875rem;">
                                        <option>This Month</option>
                                        <option>Last Month</option>
                                        <option>Last 3 Months</option>
                                        <option>Custom Range</option>
                                    </select>
                                </div>

                                <div style="display: flex; gap: 0.5rem;">
                                    <button onclick="switchPaymentView('grid')" id="grid-view-btn" style="padding: 0.5rem; border: 1px solid #e5e7eb; background: #0ea5e9; color: white; border-radius: 0.375rem; cursor: pointer; transition: all 0.2s;">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button onclick="switchPaymentView('list')" id="list-view-btn" style="padding: 0.5rem; border: 1px solid #e5e7eb; background: white; color: #6b7280; border-radius: 0.375rem; cursor: pointer; transition: all 0.2s;">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Staff Payment Grid View -->
                        <div id="staff-payment-grid-view" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 1.5rem;">
                            <!-- Staff Payment Card 1 -->
                            <div style="background: white; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden; transition: transform 0.2s, box-shadow 0.2s;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px -5px rgba(0, 0, 0, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                                <div style="padding: 1.5rem;">
                                    <div style="display: flex; items-center; justify-content: space-between; margin-bottom: 1rem;">
                                        <div style="display: flex; items-center; gap: 1rem;">
                                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #0ea5e9, #0284c7); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.125rem;">JD</div>
                                            <div>
                                                <h3 style="font-weight: 600; color: #111827; margin: 0; font-size: 1.125rem;">John Doe</h3>
                                                <p style="color: #6b7280; margin: 0; font-size: 0.875rem;">Farm Supervisor</p>
                                            </div>
                                        </div>
                                        <span style="background: #dcfce7; color: #166534; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Paid</span>
                                    </div>
                                    
                                    <div style="background: #f8fafc; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Monthly Salary:</span>
                                            <span style="color: #111827; font-weight: 600; font-size: 0.875rem;">₹45,000</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Overtime:</span>
                                            <span style="color: #111827; font-weight: 600; font-size: 0.875rem;">₹3,200</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Deductions:</span>
                                            <span style="color: #ef4444; font-weight: 600; font-size: 0.875rem;">-₹1,500</span>
                                        </div>
                                        <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid #e5e7eb;">
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: #111827; font-weight: 600; font-size: 1rem;">Net Pay:</span>
                                            <span style="color: #10b981; font-weight: bold; font-size: 1.125rem;">₹46,700</span>
                                        </div>
                                    </div>

                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; items-center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                            <i class="fas fa-calendar" style="color: #6b7280; width: 1rem;"></i>
                                            <span style="color: #374151; font-size: 0.875rem;">Payment Date: Dec 1, 2024</span>
                                        </div>
                                        <div style="display: flex; items-center; gap: 0.5rem;">
                                            <i class="fas fa-university" style="color: #6b7280; width: 1rem;"></i>
                                            <span style="color: #374151; font-size: 0.875rem;">Bank Transfer - HDFC ***1234</span>
                                        </div>
                                    </div>

                                    <div style="display: flex; gap: 0.5rem;">
                                        <button onclick="viewPaymentDetails('john-doe')" style="flex: 1; background: #f3f4f6; color: #374151; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#e5e7eb'" onmouseout="this.style.backgroundColor='#f3f4f6'">
                                            <i class="fas fa-eye"></i> Details
                                        </button>
                                        <button onclick="processPayment('john-doe')" style="flex: 1; background: #0ea5e9; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#0284c7'" onmouseout="this.style.backgroundColor='#0ea5e9'">
                                            <i class="fas fa-credit-card"></i> Pay
                                        </button>
                                        <button onclick="downloadSlip('john-doe')" style="flex: 1; background: #10b981; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#059669'" onmouseout="this.style.backgroundColor='#10b981'">
                                            <i class="fas fa-download"></i> Slip
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Staff Payment Card 2 -->
                            <div style="background: white; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden; transition: transform 0.2s, box-shadow 0.2s;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px -5px rgba(0, 0, 0, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                                <div style="padding: 1.5rem;">
                                    <div style="display: flex; items-center; justify-content: space-between; margin-bottom: 1rem;">
                                        <div style="display: flex; items-center; gap: 1rem;">
                                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.125rem;">SA</div>
                                            <div>
                                                <h3 style="font-weight: 600; color: #111827; margin: 0; font-size: 1.125rem;">Sarah Anderson</h3>
                                                <p style="color: #6b7280; margin: 0; font-size: 0.875rem;">Pond Technician</p>
                                            </div>
                                        </div>
                                        <span style="background: #fef3c7; color: #92400e; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Pending</span>
                                    </div>
                                    
                                    <div style="background: #f8fafc; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Monthly Salary:</span>
                                            <span style="color: #111827; font-weight: 600; font-size: 0.875rem;">₹38,000</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Overtime:</span>
                                            <span style="color: #111827; font-weight: 600; font-size: 0.875rem;">₹2,800</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Deductions:</span>
                                            <span style="color: #ef4444; font-weight: 600; font-size: 0.875rem;">-₹1,200</span>
                                        </div>
                                        <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid #e5e7eb;">
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: #111827; font-weight: 600; font-size: 1rem;">Net Pay:</span>
                                            <span style="color: #f59e0b; font-weight: bold; font-size: 1.125rem;">₹39,600</span>
                                        </div>
                                    </div>

                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; items-center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                            <i class="fas fa-clock" style="color: #f59e0b; width: 1rem;"></i>
                                            <span style="color: #374151; font-size: 0.875rem;">Due: Dec 5, 2024</span>
                                        </div>
                                        <div style="display: flex; items-center; gap: 0.5rem;">
                                            <i class="fas fa-university" style="color: #6b7280; width: 1rem;"></i>
                                            <span style="color: #374151; font-size: 0.875rem;">Bank Transfer - SBI ***5678</span>
                                        </div>
                                    </div>

                                    <div style="display: flex; gap: 0.5rem;">
                                        <button onclick="viewPaymentDetails('sarah-anderson')" style="flex: 1; background: #f3f4f6; color: #374151; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#e5e7eb'" onmouseout="this.style.backgroundColor='#f3f4f6'">
                                            <i class="fas fa-eye"></i> Details
                                        </button>
                                        <button onclick="processPayment('sarah-anderson')" style="flex: 1; background: #f59e0b; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#d97706'" onmouseout="this.style.backgroundColor='#f59e0b'">
                                            <i class="fas fa-credit-card"></i> Pay Now
                                        </button>
                                        <button onclick="downloadSlip('sarah-anderson')" style="flex: 1; background: #6b7280; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#4b5563'" onmouseout="this.style.backgroundColor='#6b7280'">
                                            <i class="fas fa-download"></i> Preview
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- Staff Payment Card 3 -->
                            <div style="background: white; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden; transition: transform 0.2s, box-shadow 0.2s;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px -5px rgba(0, 0, 0, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                                <div style="padding: 1.5rem;">
                                    <div style="display: flex; items-center; justify-content: space-between; margin-bottom: 1rem;">
                                        <div style="display: flex; items-center; gap: 1rem;">
                                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.125rem;">MJ</div>
                                            <div>
                                                <h3 style="font-weight: 600; color: #111827; margin: 0; font-size: 1.125rem;">Mike Johnson</h3>
                                                <p style="color: #6b7280; margin: 0; font-size: 0.875rem;">Quality Inspector</p>
                                            </div>
                                        </div>
                                        <span style="background: #fee2e2; color: #991b1b; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Overdue</span>
                                    </div>
                                    
                                    <div style="background: #f8fafc; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Monthly Salary:</span>
                                            <span style="color: #111827; font-weight: 600; font-size: 0.875rem;">₹42,000</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Overtime:</span>
                                            <span style="color: #111827; font-weight: 600; font-size: 0.875rem;">₹1,800</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Deductions:</span>
                                            <span style="color: #ef4444; font-weight: 600; font-size: 0.875rem;">-₹2,000</span>
                                        </div>
                                        <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid #e5e7eb;">
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: #111827; font-weight: 600; font-size: 1rem;">Net Pay:</span>
                                            <span style="color: #ef4444; font-weight: bold; font-size: 1.125rem;">₹41,800</span>
                                        </div>
                                    </div>

                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; items-center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                            <i class="fas fa-exclamation-triangle" style="color: #ef4444; width: 1rem;"></i>
                                            <span style="color: #374151; font-size: 0.875rem;">Overdue: Nov 28, 2024</span>
                                        </div>
                                        <div style="display: flex; items-center; gap: 0.5rem;">
                                            <i class="fas fa-university" style="color: #6b7280; width: 1rem;"></i>
                                            <span style="color: #374151; font-size: 0.875rem;">Bank Transfer - ICICI ***9012</span>
                                        </div>
                                    </div>

                                    <div style="display: flex; gap: 0.5rem;">
                                        <button onclick="viewPaymentDetails('mike-johnson')" style="flex: 1; background: #f3f4f6; color: #374151; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#e5e7eb'" onmouseout="this.style.backgroundColor='#f3f4f6'">
                                            <i class="fas fa-eye"></i> Details
                                        </button>
                                        <button onclick="processPayment('mike-johnson')" style="flex: 1; background: #ef4444; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#dc2626'" onmouseout="this.style.backgroundColor='#ef4444'">
                                            <i class="fas fa-credit-card"></i> Pay Urgent
                                        </button>
                                        <button onclick="downloadSlip('mike-johnson')" style="flex: 1; background: #6b7280; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#4b5563'" onmouseout="this.style.backgroundColor='#6b7280'">
                                            <i class="fas fa-download"></i> Preview
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Staff Payment Card 4 -->
                            <div style="background: white; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden; transition: transform 0.2s, box-shadow 0.2s;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px -5px rgba(0, 0, 0, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                                <div style="padding: 1.5rem;">
                                    <div style="display: flex; items-center; justify-content: space-between; margin-bottom: 1rem;">
                                        <div style="display: flex; items-center; gap: 1rem;">
                                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.125rem;">EW</div>
                                            <div>
                                                <h3 style="font-weight: 600; color: #111827; margin: 0; font-size: 1.125rem;">Emily Wilson</h3>
                                                <p style="color: #6b7280; margin: 0; font-size: 0.875rem;">Feed Specialist</p>
                                            </div>
                                        </div>
                                        <span style="background: #e0f2fe; color: #0277bd; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Processing</span>
                                    </div>
                                    
                                    <div style="background: #f8fafc; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Monthly Salary:</span>
                                            <span style="color: #111827; font-weight: 600; font-size: 0.875rem;">₹35,000</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Overtime:</span>
                                            <span style="color: #111827; font-weight: 600; font-size: 0.875rem;">₹2,400</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #6b7280; font-size: 0.875rem;">Deductions:</span>
                                            <span style="color: #ef4444; font-weight: 600; font-size: 0.875rem;">-₹900</span>
                                        </div>
                                        <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid #e5e7eb;">
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: #111827; font-weight: 600; font-size: 1rem;">Net Pay:</span>
                                            <span style="color: #0277bd; font-weight: bold; font-size: 1.125rem;">₹36,500</span>
                                        </div>
                                    </div>

                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; items-center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                            <i class="fas fa-sync-alt" style="color: #0277bd; width: 1rem;"></i>
                                            <span style="color: #374151; font-size: 0.875rem;">Processing: Dec 3, 2024</span>
                                        </div>
                                        <div style="display: flex; items-center; gap: 0.5rem;">
                                            <i class="fas fa-university" style="color: #6b7280; width: 1rem;"></i>
                                            <span style="color: #374151; font-size: 0.875rem;">Bank Transfer - Axis ***3456</span>
                                        </div>
                                    </div>

                                    <div style="display: flex; gap: 0.5rem;">
                                        <button onclick="viewPaymentDetails('emily-wilson')" style="flex: 1; background: #f3f4f6; color: #374151; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#e5e7eb'" onmouseout="this.style.backgroundColor='#f3f4f6'">
                                            <i class="fas fa-eye"></i> Details
                                        </button>
                                        <button onclick="trackPayment('emily-wilson')" style="flex: 1; background: #0277bd; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#01579b'" onmouseout="this.style.backgroundColor='#0277bd'">
                                            <i class="fas fa-search"></i> Track
                                        </button>
                                        <button onclick="downloadSlip('emily-wilson')" style="flex: 1; background: #6b7280; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#4b5563'" onmouseout="this.style.backgroundColor='#6b7280'">
                                            <i class="fas fa-download"></i> Preview
                                        </button>
                                    </div>
                                </div>
                            </div>
                                        <button onclick="assignTask('emily-wilson')" style="flex: 1; background: #10b981; color: white; padding: 0.5rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#059669'" onmouseout="this.style.backgroundColor='#10b981'">
                                            <i class="fas fa-tasks"></i> Assign
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment List View (Hidden by default) -->
                        <div id="staff-payment-list-view" style="display: none;">
                            <div style="background: white; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead style="background: #f9fafb;">
                                        <tr>
                                            <th style="padding: 1rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">Staff Member</th>
                                            <th style="padding: 1rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">Department</th>
                                            <th style="padding: 1rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">Salary</th>
                                            <th style="padding: 1rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">Net Pay</th>
                                            <th style="padding: 1rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">Status</th>
                                            <th style="padding: 1rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">Due Date</th>
                                            <th style="padding: 1rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr style="border-top: 1px solid #e5e7eb;">
                                            <td style="padding: 1rem;">
                                                <div style="display: flex; items-center; gap: 0.75rem;">
                                                    <div style="width: 2.5rem; height: 2.5rem; background: linear-gradient(135deg, #0ea5e9, #0284c7); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">JD</div>
                                                    <div>
                                                        <div style="font-weight: 500; color: #111827;">John Doe</div>
                                                        <div style="color: #6b7280; font-size: 0.875rem;">Farm Supervisor</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td style="padding: 1rem; color: #374151;">Farm Operations</td>
                                            <td style="padding: 1rem; color: #374151;">₹45,000</td>
                                            <td style="padding: 1rem; color: #10b981; font-weight: 600;">₹46,700</td>
                                            <td style="padding: 1rem;">
                                                <span style="background: #dcfce7; color: #166534; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Paid</span>
                                            </td>
                                            <td style="padding: 1rem; color: #374151;">Dec 1, 2024</td>
                                            <td style="padding: 1rem;">
                                                <div style="display: flex; gap: 0.5rem;">
                                                    <button style="padding: 0.25rem 0.5rem; background: #f3f4f6; color: #374151; border: none; border-radius: 0.25rem; cursor: pointer; font-size: 0.75rem;"><i class="fas fa-eye"></i></button>
                                                    <button style="padding: 0.25rem 0.5rem; background: #0ea5e9; color: white; border: none; border-radius: 0.25rem; cursor: pointer; font-size: 0.75rem;"><i class="fas fa-credit-card"></i></button>
                                                    <button style="padding: 0.25rem 0.5rem; background: #10b981; color: white; border: none; border-radius: 0.25rem; cursor: pointer; font-size: 0.75rem;"><i class="fas fa-download"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr style="border-top: 1px solid #e5e7eb;">
                                            <td style="padding: 1rem;">
                                                <div style="display: flex; items-center; gap: 0.75rem;">
                                                    <div style="width: 2.5rem; height: 2.5rem; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">SA</div>
                                                    <div>
                                                        <div style="font-weight: 500; color: #111827;">Sarah Anderson</div>
                                                        <div style="color: #6b7280; font-size: 0.875rem;">Pond Technician</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td style="padding: 1rem; color: #374151;">Pond Management</td>
                                            <td style="padding: 1rem; color: #374151;">₹38,000</td>
                                            <td style="padding: 1rem; color: #f59e0b; font-weight: 600;">₹39,600</td>
                                            <td style="padding: 1rem;">
                                                <span style="background: #fef3c7; color: #92400e; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;">Pending</span>
                                            </td>
                                            <td style="padding: 1rem; color: #374151;">Dec 5, 2024</td>
                                            <td style="padding: 1rem;">
                                                <div style="display: flex; gap: 0.5rem;">
                                                    <button style="padding: 0.25rem 0.5rem; background: #f3f4f6; color: #374151; border: none; border-radius: 0.25rem; cursor: pointer; font-size: 0.75rem;"><i class="fas fa-eye"></i></button>
                                                    <button style="padding: 0.25rem 0.5rem; background: #f59e0b; color: white; border: none; border-radius: 0.25rem; cursor: pointer; font-size: 0.75rem;"><i class="fas fa-credit-card"></i></button>
                                                    <button style="padding: 0.25rem 0.5rem; background: #6b7280; color: white; border: none; border-radius: 0.25rem; cursor: pointer; font-size: 0.75rem;"><i class="fas fa-download"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div style="margin-top: 2rem; display: flex; justify-content: between; align-items: center;">
                            <div style="color: #6b7280; font-size: 0.875rem;">
                                Showing 1 to 10 of 24 payment records
                            </div>
                            <div style="display: flex; gap: 0.5rem;">
                                <button style="padding: 0.5rem 1rem; border: 1px solid #d1d5db; background: white; color: #374151; border-radius: 0.375rem; cursor: pointer; font-size: 0.875rem;" disabled>Previous</button>
                                <button style="padding: 0.5rem 1rem; background: #0ea5e9; color: white; border: none; border-radius: 0.375rem; cursor: pointer; font-size: 0.875rem;">1</button>
                                <button style="padding: 0.5rem 1rem; border: 1px solid #d1d5db; background: white; color: #374151; border-radius: 0.375rem; cursor: pointer; font-size: 0.875rem;">2</button>
                                <button style="padding: 0.5rem 1rem; border: 1px solid #d1d5db; background: white; color: #374151; border-radius: 0.375rem; cursor: pointer; font-size: 0.875rem;">3</button>
                                <button style="padding: 0.5rem 1rem; border: 1px solid #d1d5db; background: white; color: #374151; border-radius: 0.375rem; cursor: pointer; font-size: 0.875rem;">Next</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Staff Payment Functions
        function switchPaymentView(viewType) {
            const gridView = document.getElementById('staff-payment-grid-view');
            const listView = document.getElementById('staff-payment-list-view');
            const gridBtn = document.getElementById('grid-view-btn');
            const listBtn = document.getElementById('list-view-btn');

            if (viewType === 'grid') {
                gridView.style.display = 'grid';
                listView.style.display = 'none';
                gridBtn.style.background = '#0ea5e9';
                gridBtn.style.color = 'white';
                listBtn.style.background = 'white';
                listBtn.style.color = '#6b7280';
            } else {
                gridView.style.display = 'none';
                listView.style.display = 'block';
                listBtn.style.background = '#0ea5e9';
                listBtn.style.color = 'white';
                gridBtn.style.background = 'white';
                gridBtn.style.color = '#6b7280';
            }
        }

        function openPaymentModal() {
            alert('Process Payment Modal would open here. This would contain a form with:\n\n• Staff Selection\n• Payment Amount\n• Payment Method\n• Bank Details\n• Payment Date\n• Reference Number\n• Notes/Remarks\n• Approval Workflow');
        }

        function viewPaymentDetails(staffId) {
            alert(`Viewing payment details for: ${staffId}\n\nThis would show:\n• Salary Breakdown\n• Payment History\n• Tax Deductions\n• Overtime Calculations\n• Bank Details\n• Payment Status\n• Transaction Reference\n• Approval Trail`);
        }

        function processPayment(staffId) {
            alert(`Process payment for: ${staffId}\n\nThis would open payment form with:\n• Payment amount verification\n• Bank transfer details\n• Payment method selection\n• Reference number generation\n• Approval workflow\n• Payment scheduling`);
        }

        function downloadSlip(staffId) {
            alert(`Download payment slip for: ${staffId}\n\nThis would generate:\n• PDF pay slip\n• Salary certificate\n• Tax declaration\n• Bank statement\n• Payment confirmation\n• Deduction summary`);
        }

        function trackPayment(staffId) {
            alert(`Track payment for: ${staffId}\n\nThis would show:\n• Payment initiation timestamp\n• Bank processing status\n• Transaction reference number\n• Expected completion time\n• Real-time status updates\n• Contact bank support`);
        }

        function exportPaymentData() {
            alert('Exporting payment data...\n\nThis would generate:\n• Excel/CSV file\n• Payroll summary\n• Tax reports\n• Payment history\n• Department-wise breakdown\n• Monthly statements\n• Bank transfer file');
        }

        // Settings dropdown functions
        function showListOfMaterials() {
            console.log('List of materials selected');
        }

        function showUnitsConfig() {
            console.log('Units config selected');
            const mainContent = document.querySelector('.main-content .content-container');
            if (mainContent) {
                mainContent.innerHTML = `
                    <div style="background: #f8fafc; min-height: calc(100vh - 130px); padding: 24px;">
                        <!-- Breadcrumb -->
                        <nav style="margin-bottom: 24px;">
                            <ol style="display: flex; align-items: center; list-style: none; padding: 0; margin: 0; font-size: 14px; color: #6b7280;">
                                <li><a href="#" style="color: #374151; text-decoration: none; font-weight: 500;" onmouseover="this.style.color='#1f2937'" onmouseout="this.style.color='#374151'">Home</a></li>
                                <li style="margin: 0 8px; color: #9ca3af;">></li>
                                <li style="color: #6b7280;">List of supply units</li>
                            </ol>
                        </nav>

                        <!-- Header Section with Title and Actions -->
                        <div style="display: flex; flex-direction: column; gap: 20px; margin-bottom: 24px;">
                            <!-- Top Row: Title and Create Button -->
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <h1 style="font-size: 1.875rem; font-weight: 600; color: #111827; margin: 0;">List of supply units</h1>
                                <button style="background: #f97316; color: white; padding: 12px 20px; border-radius: 10px; border: none; cursor: pointer; font-weight: 600; font-size: 14px; display: flex; align-items: center; gap: 8px; transition: all 0.3s; box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);" onmouseover="this.style.backgroundColor='#ea580c'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 16px rgba(249, 115, 22, 0.4)'" onmouseout="this.style.backgroundColor='#f97316'; this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(249, 115, 22, 0.3)'">
                                    <i class="fas fa-plus" style="font-size: 12px;"></i> Create New Unit
                                </button>
                            </div>
                            
                            <!-- Bottom Row: Farm Selector -->
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="display: flex; flex-direction: column;">
                                    <label style="font-size: 12px; font-weight: 500; color: #6b7280; margin-bottom: 4px;">Filter by Farm</label>
                                    <select style="padding: 10px 32px 10px 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; color: #374151; background: white; cursor: pointer; min-width: 220px; appearance: none; background-image: url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 20 20\'%3e%3cpath stroke=\'%236b7280\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'1.5\' d=\'M6 8l4 4 4-4\'/%3e%3c/svg%3e'); background-position: right 8px center; background-repeat: no-repeat; background-size: 16px; transition: border-color 0.2s;" onfocus="this.style.borderColor='#f97316'; this.style.boxShadow='0 0 0 3px rgba(249, 115, 22, 0.1)'" onblur="this.style.borderColor='#d1d5db'; this.style.boxShadow='none'">
                                        <option value="all">All Farms</option>
                                        <option value="sikindar-shaik" selected>Sikindar Shaik</option>
                                        <option value="farm-alpha">Farm Alpha</option>
                                        <option value="farm-beta">Farm Beta</option>
                                        <option value="farm-gamma">Farm Gamma</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Table Container -->
                        <div style="background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); overflow: hidden;">
                            <!-- Table Header -->
                            <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; background: #f9fafb; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; font-size: 14px;">
                                <div>Name</div>
                                <div>Description</div>
                                <div style="text-align: center;">Actions</div>
                            </div>

                            <!-- Table Rows -->
                            <div style="divide-y divide-gray-200;">
                                <!-- Row 1 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">cái (noun)</div>
                                    <div style="color: #6b7280;"></div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 2 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Roll 100m (cuon100met)</div>
                                    <div style="color: #6b7280;">= 100 mét</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 3 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Roll 50m (cuon50met)</div>
                                    <div style="color: #6b7280;">= 50 mét</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 4 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Roll 40m (cuon40met)</div>
                                    <div style="color: #6b7280;">= 40 mét</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 5 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Bag 25kg (bao25kg)</div>
                                    <div style="color: #6b7280;">= 25 kg</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 6 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Bag 2kg (bao2kg)</div>
                                    <div style="color: #6b7280;">= 2 kg</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 7 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Bag 18kg (bao18kg)</div>
                                    <div style="color: #6b7280;">= 18 kg</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 8 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Bag 50kg (bao50kg)</div>
                                    <div style="color: #6b7280;">= 50 kg</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 9 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Bag 20kg (bao20kg)</div>
                                    <div style="color: #6b7280;">= 20 kg</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row 10 -->
                                <div style="display: grid; grid-template-columns: 2fr 2fr 1fr; gap: 16px; padding: 16px 24px; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='white'">
                                    <div style="color: #374151; font-weight: 500;">Bag 5kg (bao5kg)</div>
                                    <div style="color: #6b7280;">= 5 kg</div>
                                    <div style="text-align: center;">
                                        <button style="background: none; border: none; cursor: pointer; padding: 8px; border-radius: 4px; color: #6b7280; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#374151'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#6b7280'">
                                            <i class="fas fa-edit" style="font-size: 14px;"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 24px;">
                            <div></div>
                            <div style="display: flex; align-items: center; gap: 16px;">
                                <div style="display: flex; align-items: center; gap: 8px; font-size: 14px; color: #6b7280;">
                                    <span>Items per page</span>
                                    <select style="padding: 4px 24px 4px 8px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; color: #374151; background: white; cursor: pointer; appearance: none; background-image: url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 20 20\'%3e%3cpath stroke=\'%236b7280\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'1.5\' d=\'M6 8l4 4 4-4\'/%3e%3c/svg%3e'); background-position: right 4px center; background-repeat: no-repeat; background-size: 12px;">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <button style="background: none; border: 1px solid #d1d5db; color: #6b7280; padding: 6px 8px; border-radius: 6px; cursor: pointer; font-size: 14px; display: flex; align-items: center; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                        <i class="fas fa-chevron-left" style="font-size: 12px;"></i>
                                    </button>
                                    <button style="background: none; border: 1px solid #d1d5db; color: #6b7280; padding: 6px 8px; border-radius: 6px; cursor: pointer; font-size: 14px; display: flex; align-items: center; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                        <i class="fas fa-chevron-right" style="font-size: 12px;"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        function showSeason() {
            console.log('Season selected');
        }

        function showFarmingProcess() {
            console.log('Farming process selected');
        }

        function showApprovalProcess() {
            console.log('Approval process selected');
        }

        function showImportExportFile() {
            console.log('Import/export file selected');
        }

        function showCertificationProfile() {
            console.log('Certification Profile selected');
        }

        function showSwitchAppMode() {
            console.log('Switch app mode selected');
        }

        function showToggleFeature() {
            console.log('Toggle Feature selected');
            const mainContent = document.querySelector('.main-content .content-container');
            if (mainContent) {
                mainContent.innerHTML = `
                    <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); font-family: 'Inter', sans-serif; padding: 16px; min-height: calc(100vh - 130px);">
                        <div style="max-width: 100%; margin: 0 auto;">
                            <!-- Breadcrumb -->
                            <nav style="margin-bottom: 16px;">
                                <ol class="breadcrumb flex items-center text-sm" style="color: #64748b;">
                                    <li><a href="#" style="color: #2563eb; font-weight: 500; text-decoration: none;" onmouseover="this.style.color='#1d4ed8'" onmouseout="this.style.color='#2563eb'">Home</a></li>
                                    <li class="mx-2"><i class="fas fa-chevron-right" style="font-size: 0.75rem;"></i></li>
                                    <li><a href="#" style="color: #2563eb; font-weight: 500; text-decoration: none;" onmouseover="this.style.color='#1d4ed8'" onmouseout="this.style.color='#2563eb'">Settings</a></li>
                                    <li class="mx-2"><i class="fas fa-chevron-right" style="font-size: 0.75rem;"></i></li>
                                    <li style="color: #64748b;">Feature Management</li>
                                </ol>
                            </nav>

                            <!-- Header -->
                            <div style="margin-bottom: 20px; text-align: center;">
                                <h1 style="font-size: 1.75rem; font-weight: bold; color: #1e293b; margin-bottom: 8px;">Feature Management</h1>
                                <p style="color: #64748b; font-size: 14px;">Customize your experience by enabling or disabling features.</p>
                            </div>

                            <!-- Feature Cards in Grid Layout -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(480px, 1fr)); gap: 16px; margin-bottom: 24px;">
                                <!-- Manage Shrimp Farm -->
                                <div class="feature-card" style="background: white; border-radius: 12px; padding: 20px; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.05)';">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 12px;">
                                        <div style="background: #dbeafe; padding: 10px; border-radius: 8px; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-fish" style="color: #2563eb; font-size: 18px;"></i>
                                        </div>
                                        <div style="flex: 1; min-width: 0;">
                                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                                <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin: 0;">Manage Shrimp Farm</h3>
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                    <span style="padding: 2px 8px; background: #dcfce7; color: #166534; font-size: 11px; font-weight: 500; border-radius: 12px;">Active</span>
                                                    <div class="toggle-switch" style="position: relative; display: inline-block; width: 42px; height: 22px;">
                                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                                        <span class="slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 22px;" onclick="toggleFeatureSwitch(this)">
                                                            <span style="position: absolute; content: ''; height: 16px; width: 16px; left: 23px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; display: block;"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #64748b; font-size: 13px; line-height: 1.4; margin-bottom: 12px;">
                                        Enable farm management tools, digital pond logbooks, task delegation, shrimp measurement tools, environmental tracking, equipment control, inventory management, and smart reporting.
                                    </p>
                                    <div style="display: flex; gap: 6px; margin-bottom: 12px;">
                                        <button class="tab-button active" style="padding: 6px 12px; border-radius: 6px; font-size: 12px; font-weight: 500; background-color: #3b82f6; color: white; border: none; cursor: pointer; transition: all 0.2s ease;" onclick="switchFeatureTab(this, 'all-farms')">All farms</button>
                                        <button class="tab-button" style="padding: 6px 12px; border-radius: 6px; font-size: 12px; font-weight: 500; background-color: transparent; color: #1e293b; border: none; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='#e2e8f0'" onmouseout="this.style.backgroundColor='transparent'" onclick="switchFeatureTab(this, 'by-farm')">By farm</button>
                                    </div>
                                    <!-- Farm Selector (initially hidden) -->
                                    <div id="farm-selector" style="display: none; margin-top: 8px;">
                                        <div style="display: flex; flex-direction: column; gap: 8px;">
                                            <label style="font-size: 12px; font-weight: 500; color: #6b7280;">Farm</label>
                                            <div style="position: relative;">
                                                <select style="width: 100%; padding: 8px 32px 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 13px; color: #374151; background: white; cursor: pointer; appearance: none; background-image: url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 20 20\'%3e%3cpath stroke=\'%236b7280\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'1.5\' d=\'M6 8l4 4 4-4\'/%3e%3c/svg%3e'); background-position: right 8px center; background-repeat: no-repeat; background-size: 16px;">
                                                    <option value="puttlacheruvu">puttlacheruvu</option>
                                                    <option value="farm-2">Farm Alpha</option>
                                                    <option value="farm-3">Farm Beta</option>
                                                    <option value="farm-4">Farm Gamma</option>
                                                    <option value="farm-5">Farm Delta</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Manage Testlab -->
                                <div class="feature-card" style="background: white; border-radius: 12px; padding: 20px; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.05)';">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 12px;">
                                        <div style="background: #f3e8ff; padding: 10px; border-radius: 8px; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-vial" style="color: #7c3aed; font-size: 18px;"></i>
                                        </div>
                                        <div style="flex: 1; min-width: 0;">
                                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                                <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin: 0;">Manage Testlab</h3>
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                    <span style="padding: 2px 8px; background: #dcfce7; color: #166534; font-size: 11px; font-weight: 500; border-radius: 12px;">Active</span>
                                                    <div class="toggle-switch" style="position: relative; display: inline-block; width: 42px; height: 22px;">
                                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                                        <span class="slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 22px;" onclick="toggleFeatureSwitch(this)">
                                                            <span style="position: absolute; content: ''; height: 16px; width: 16px; left: 23px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; display: block;"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #64748b; font-size: 13px; line-height: 1.4;">
                                        Receive registration testing information for bacteria, disease, PCR directly from farm/hatchery and return results online.
                                    </p>
                                </div>

                                <!-- Device Control -->
                                <div class="feature-card" style="background: white; border-radius: 12px; padding: 20px; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.05)';">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 12px;">
                                        <div style="background: #e0e7ff; padding: 10px; border-radius: 8px; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-microchip" style="color: #4f46e5; font-size: 18px;"></i>
                                        </div>
                                        <div style="flex: 1; min-width: 0;">
                                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                                <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin: 0;">Device Control</h3>
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                    <span style="padding: 2px 8px; background: #dcfce7; color: #166534; font-size: 11px; font-weight: 500; border-radius: 12px;">Active</span>
                                                    <div class="toggle-switch" style="position: relative; display: inline-block; width: 42px; height: 22px;">
                                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                                        <span class="slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 22px;" onclick="toggleFeatureSwitch(this)">
                                                            <span style="position: absolute; content: ''; height: 16px; width: 16px; left: 23px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; display: block;"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #64748b; font-size: 13px; line-height: 1.4;">
                                        Remote control of pond devices, scheduling operations, and alerting for malfunctions through Tomota's automatic control system.
                                    </p>
                                </div>

                                <!-- Task Management -->
                                <div class="feature-card" style="background: white; border-radius: 12px; padding: 20px; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.05)';">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 12px;">
                                        <div style="background: #fef3c7; padding: 10px; border-radius: 8px; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-tasks" style="color: #f59e0b; font-size: 18px;"></i>
                                        </div>
                                        <div style="flex: 1; min-width: 0;">
                                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                                <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin: 0;">Task Management</h3>
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                    <span style="padding: 2px 8px; background: #dcfce7; color: #166534; font-size: 11px; font-weight: 500; border-radius: 12px;">Active</span>
                                                    <div class="toggle-switch" style="position: relative; display: inline-block; width: 42px; height: 22px;">
                                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                                        <span class="slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 22px;" onclick="toggleFeatureSwitch(this)">
                                                            <span style="position: absolute; content: ''; height: 16px; width: 16px; left: 23px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; display: block;"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #64748b; font-size: 13px; line-height: 1.4;">
                                        Assign daily tasks to employees and support workers. Keep farming event logs even if task management is turned off.
                                    </p>
                                </div>

                                <!-- Zoning for Farming -->
                                <div class="feature-card" style="background: white; border-radius: 12px; padding: 20px; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.05)';">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 12px;">
                                        <div style="background: #d1fae5; padding: 10px; border-radius: 8px; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-map-marked-alt" style="color: #059669; font-size: 18px;"></i>
                                        </div>
                                        <div style="flex: 1; min-width: 0;">
                                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                                <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin: 0;">Zoning for Farming</h3>
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                    <span style="padding: 2px 8px; background: #dcfce7; color: #166534; font-size: 11px; font-weight: 500; border-radius: 12px;">Active</span>
                                                    <div class="toggle-switch" style="position: relative; display: inline-block; width: 42px; height: 22px;">
                                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                                        <span class="slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 22px;" onclick="toggleFeatureSwitch(this)">
                                                            <span style="position: absolute; content: ''; height: 16px; width: 16px; left: 23px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; display: block;"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #64748b; font-size: 13px; line-height: 1.4;">
                                        Organize your farm into different management zones for larger farms. If disabled, manage at farm and pond levels.
                                    </p>
                                </div>

                                <!-- Warehouse Management -->
                                <div class="feature-card" style="background: white; border-radius: 12px; padding: 20px; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.05)';">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 12px;">
                                        <div style="background: #ffe4e6; padding: 10px; border-radius: 8px; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-warehouse" style="color: #e11d48; font-size: 18px;"></i>
                                        </div>
                                        <div style="flex: 1; min-width: 0;">
                                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                                <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin: 0;">Warehouse Management</h3>
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                    <span style="padding: 2px 8px; background: #dcfce7; color: #166534; font-size: 11px; font-weight: 500; border-radius: 12px;">Active</span>
                                                    <div class="toggle-switch" style="position: relative; display: inline-block; width: 42px; height: 22px;">
                                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                                        <span class="slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 22px;" onclick="toggleFeatureSwitch(this)">
                                                            <span style="position: absolute; content: ''; height: 16px; width: 16px; left: 23px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; display: block;"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #64748b; font-size: 13px; line-height: 1.4;">
                                        Manage food and supplies warehouse including inventory, stock tracking, and usage history for cost control and profit calculation.
                                    </p>
                                </div>

                                <!-- Allow Staff to Use Point -->
                                <div class="feature-card" style="background: white; border-radius: 12px; padding: 20px; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.05)';">
                                    <div style="display: flex; align-items: flex-start; margin-bottom: 12px;">
                                        <div style="background: #cffafe; padding: 10px; border-radius: 8px; margin-right: 12px; flex-shrink: 0;">
                                            <i class="fas fa-user-check" style="color: #0891b2; font-size: 18px;"></i>
                                        </div>
                                        <div style="flex: 1; min-width: 0;">
                                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                                                <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin: 0;">Allow Staff to Use Point</h3>
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                    <span style="padding: 2px 8px; background: #dcfce7; color: #166534; font-size: 11px; font-weight: 500; border-radius: 12px;">Active</span>
                                                    <div class="toggle-switch" style="position: relative; display: inline-block; width: 42px; height: 22px;">
                                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                                        <span class="slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 22px;" onclick="toggleFeatureSwitch(this)">
                                                            <span style="position: absolute; content: ''; height: 16px; width: 16px; left: 23px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; display: block;"></span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #64748b; font-size: 13px; line-height: 1.4;">
                                        Allow staff to use points for rewards and recognition programs.
                                    </p>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div style="display: flex; justify-content: flex-end; gap: 12px;">
                                <button style="padding: 10px 20px; border: 1px solid #cbd5e1; color: #334155; border-radius: 8px; background: white; cursor: pointer; font-weight: 500; font-size: 14px; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#f1f5f9'" onmouseout="this.style.backgroundColor='white'">
                                    Cancel
                                </button>
                                <button style="padding: 10px 20px; background: #2563eb; color: white; border-radius: 8px; border: none; cursor: pointer; font-weight: 500; font-size: 14px; display: flex; align-items: center; justify-content: center; transition: all 0.2s;" onmouseover="this.style.backgroundColor='#1d4ed8'" onmouseout="this.style.backgroundColor='#2563eb'">
                                    <i class="fas fa-save" style="margin-right: 6px; font-size: 12px;"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Helper function to toggle feature switches
        function toggleFeatureSwitch(slider) {
            const input = slider.previousElementSibling;
            const isChecked = input.checked;
            
            // Toggle the input state
            input.checked = !isChecked;
            
            // Update slider appearance
            if (input.checked) {
                slider.style.backgroundColor = '#3b82f6';
                slider.querySelector('span').style.left = '23px';
            } else {
                slider.style.backgroundColor = '#cbd5e1';
                slider.querySelector('span').style.left = '3px';
            }
            
            // Update status badge
            const statusBadge = slider.closest('.feature-card').querySelector('span[style*="background: #dcfce7"]');
            if (statusBadge) {
                if (input.checked) {
                    statusBadge.textContent = 'Active';
                    statusBadge.style.background = '#dcfce7';
                    statusBadge.style.color = '#166534';
                } else {
                    statusBadge.textContent = 'Inactive';
                    statusBadge.style.background = '#fee2e2';
                    statusBadge.style.color = '#991b1b';
                }
            }
        }

        // Helper function to switch feature tabs
        function switchFeatureTab(button, tabType) {
            // Remove active class from all tab buttons in the same container
            const container = button.parentNode;
            const allTabs = container.querySelectorAll('.tab-button');
            
            allTabs.forEach(tab => {
                tab.style.backgroundColor = 'transparent';
                tab.style.color = '#1e293b';
                tab.classList.remove('active');
            });
            
            // Add active class to clicked button
            button.style.backgroundColor = '#3b82f6';
            button.style.color = 'white';
            button.classList.add('active');
            
            // Show/hide farm selector based on tab type
            const farmSelector = document.getElementById('farm-selector');
            if (farmSelector) {
                if (tabType === 'by-farm') {
                    farmSelector.style.display = 'block';
                    // Add smooth fade-in animation
                    farmSelector.style.opacity = '0';
                    setTimeout(() => {
                        farmSelector.style.transition = 'opacity 0.3s ease';
                        farmSelector.style.opacity = '1';
                    }, 10);
                } else {
                    farmSelector.style.display = 'none';
                    farmSelector.style.opacity = '0';
                }
            }
        }

        function toggleSidebar() {
            const sidebar = document.querySelector('.tomota-sidebar');
            const mainContent = document.querySelector('.main-content');
            const header = document.querySelector('.header-section');

            sidebar.classList.toggle('hidden');
            mainContent.classList.toggle('sidebar-hidden');
            header.classList.toggle('sidebar-hidden');
        }

        function switchTab(tabName, element) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.content-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            const selectedContent = document.getElementById(tabName + '-content');
            if (selectedContent) {
                selectedContent.style.display = 'block';
            }

            // Add active class to clicked tab
            element.classList.add('active');
        }

        // User dropdown functionality
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                // Add logout functionality here
                alert('Logging out...');
                // You can redirect to login page or clear session
                // window.location.href = '/login';
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const avatarButton = document.querySelector('.avatar-button');

            if (!avatarButton.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Dashboard functionality
        function showDashboard() {
            // Hide all tab content sections
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });

            // Hide tabs section
            const tabsSection = document.querySelector('.content-tabs');
            if (tabsSection) {
                tabsSection.style.display = 'none';
            }

            // Show dashboard content
            const dashboardContent = document.getElementById('dashboard-content');
            if (dashboardContent) {
                dashboardContent.style.display = 'block';
            }
        }

        // Function to show tabs (for other nav items)
        function showTabs() {
            // Hide dashboard content
            const dashboardContent = document.getElementById('dashboard-content');
            if (dashboardContent) {
                dashboardContent.style.display = 'none';
            }

            // Show tabs section
            const tabsSection = document.querySelector('.content-tabs');
            if (tabsSection) {
                tabsSection.style.display = 'flex';
            }

            // Show active pond content by default
            document.getElementById('active-pond-content').style.display = 'block';
        }

        // Map functionality
        function switchMapView(viewType) {
            // Update active button
            const buttons = document.querySelectorAll('.map-control-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Simulate map view change
            console.log('Switching to ' + viewType + ' view');
            // Here you would integrate with actual map API (Google Maps, Mapbox, etc.)
        }

        function loadInteractiveMap() {
            const mapPlaceholder = document.querySelector('.map-placeholder');
            const loadBtn = document.querySelector('.load-map-btn');

            // Show loading state
            loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading Map...';
            loadBtn.disabled = true;

            // Simulate map loading
            setTimeout(() => {
                mapPlaceholder.innerHTML = `
                    <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #4CAF50 0%, #2196F3 100%); display: flex; align-items: center; justify-content: center; position: relative;">
                        <div style="text-align: center; color: white;">
                            <i class="fas fa-map-marked-alt text-6xl mb-4"></i>
                            <h3 style="margin: 0; font-size: 24px; font-weight: 600;">Interactive Map Loaded</h3>
                            <p style="margin: 8px 0 0 0; opacity: 0.9;">Sikindar Shaik Farm - Satellite View</p>
                        </div>
                        <!-- Simulated pond markers -->
                        <div style="position: absolute; top: 30%; left: 25%; width: 20px; height: 20px; background: #28a745; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond A1 - Active" onclick="focusPond('A1')"></div>
                        <div style="position: absolute; top: 45%; left: 35%; width: 20px; height: 20px; background: #28a745; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond A2 - Active" onclick="focusPond('A2')"></div>
                        <div style="position: absolute; top: 60%; left: 45%; width: 20px; height: 20px; background: #007bff; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond B1 - Stocking" onclick="focusPond('B1')"></div>
                        <div style="position: absolute; top: 35%; left: 55%; width: 20px; height: 20px; background: #6f42c1; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond B2 - Renovation" onclick="focusPond('B2')"></div>
                        <div style="position: absolute; top: 70%; left: 65%; width: 20px; height: 20px; background: #28a745; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond C1 - Active" onclick="focusPond('C1')"></div>
                    </div>
                `;
            }, 2000);
        }

        function focusPond(pondId) {
            // Highlight selected pond in sidebar
            const pondItems = document.querySelectorAll('.pond-item');
            pondItems.forEach(item => item.classList.remove('selected'));

            // Find and highlight the clicked pond
            pondItems.forEach(item => {
                if (item.textContent.includes(pondId)) {
                    item.classList.add('selected');
                    item.style.background = '#e3f2fd';
                    item.style.borderColor = '#2196f3';
                }
            });

            // Show pond details (you can expand this)
            console.log('Focusing on pond: ' + pondId);

            // You could show a popup with pond details here
            alert('Pond ' + pondId + ' selected. Detailed information would be displayed here.');
        }

        // Satellite Map Functions
        function switchView(viewType) {
            const buttons = document.querySelectorAll('.view-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            const mapView = document.querySelector('.satellite-map-view');

            if (viewType === 'satellite') {
                // Switch to satellite view
                mapView.style.filter = 'contrast(1.1) saturate(1.2)';
                console.log('Switched to satellite view');
            } else {
                // Switch to map view
                mapView.style.filter = 'contrast(0.9) saturate(0.8) hue-rotate(20deg)';
                console.log('Switched to map view');
            }
        }

        function toggleFullscreen() {
            const mapContainer = document.querySelector('.satellite-map-container');
            const fullscreenBtn = document.querySelector('.fullscreen-btn');

            if (!document.fullscreenElement) {
                mapContainer.requestFullscreen().then(() => {
                    fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
                    mapContainer.style.borderRadius = '0';
                }).catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen().then(() => {
                    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                    mapContainer.style.borderRadius = '12px';
                });
            }
        }

        function showPondInfo(pondId) {
            const pondData = {
                'A1': { name: 'Pond A1', status: 'Active', days: 44, survival: '89%', color: '#10b981' },
                'A2': { name: 'Pond A2', status: 'Active', days: 39, survival: '92%', color: '#10b981' },
                'B1': { name: 'Pond B1', status: 'Stocking', days: 1, survival: 'New stock', color: '#3b82f6' },
                'B2': { name: 'Pond B2', status: 'Renovation', days: 0, survival: 'Maintenance', color: '#f59e0b' },
                'C1': { name: 'Pond C1', status: 'Active', days: 80, survival: '75%', color: '#10b981' },
                'D1': { name: 'Pond D1', status: 'Active', days: 52, survival: '88%', color: '#10b981' },
                'E1': { name: 'Pond E1', status: 'Active', days: 28, survival: '94%', color: '#10b981' }
            };

            const pond = pondData[pondId];
            if (pond) {
                // Create info popup
                const popup = document.createElement('div');
                popup.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                    z-index: 1000;
                    min-width: 300px;
                    text-align: center;
                `;

                popup.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; gap: 10px; margin-bottom: 15px;">
                        <div style="width: 20px; height: 20px; background: ${pond.color}; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        <h3 style="margin: 0; color: #333;">${pond.name}</h3>
                    </div>
                    <p style="margin: 5px 0; color: #666;"><strong>Status:</strong> ${pond.status}</p>
                    ${pond.days > 0 ? `<p style="margin: 5px 0; color: #666;"><strong>Days:</strong> ${pond.days}</p>` : ''}
                    <p style="margin: 5px 0; color: #666;"><strong>Survival Rate:</strong> ${pond.survival}</p>
                    <button onclick="this.parentElement.remove()" style="margin-top: 15px; padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer;">Close</button>
                `;

                document.body.appendChild(popup);

                // Remove popup after 5 seconds
                setTimeout(() => {
                    if (popup.parentElement) {
                        popup.remove();
                    }
                }, 5000);
            }
        }

        function zoomIn() {
            const mapView = document.querySelector('.satellite-map-view');
            const currentScale = mapView.style.transform.match(/scale\(([\d.]+)\)/);
            const scale = currentScale ? parseFloat(currentScale[1]) : 1;
            const newScale = Math.min(scale * 1.2, 3);
            mapView.style.transform = `scale(${newScale})`;
            mapView.style.transformOrigin = 'center center';
        }

        function zoomOut() {
            const mapView = document.querySelector('.satellite-map-view');
            const currentScale = mapView.style.transform.match(/scale\(([\d.]+)\)/);
            const scale = currentScale ? parseFloat(currentScale[1]) : 1;
            const newScale = Math.max(scale / 1.2, 0.5);
            mapView.style.transform = `scale(${newScale})`;
            mapView.style.transformOrigin = 'center center';
        }

        // Modern Interface Functions
        function switchModernTab(tabName, element) {
            // Remove active class from all tabs
            document.querySelectorAll('.modern-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Add active class to clicked tab
            element.classList.add('active');

            // Show notification or handle tab content
            showNotification(`Switched to ${tabName} view`, 'info');
        }

        function showPondDetails(type) {
            showNotification(`Viewing ${type} pond details`, 'info');
            // Here you would typically load detailed pond information
        }

        function showAnalytics(type) {
            showNotification(`Loading ${type} analytics`, 'info');
            // Here you would typically load analytics data
        }

        function showEnvironmental(parameter) {
            showNotification(`Viewing ${parameter} data`, 'info');
            // Here you would typically load environmental data
        }

        function showOperations(operation) {
            showNotification(`Managing ${operation} operations`, 'info');
            // Here you would typically load operations data
        }

        function showQuickActions() {
            showNotification('Quick Actions menu opened', 'info');
            // Here you would show a quick actions modal or menu
        }

        function showNotifications() {
            showNotification('Notifications panel opened', 'info');
            // Here you would show notifications panel
        }

        function showMainMenu() {
            showNotification('Main menu opened', 'info');
            // Here you would show main menu options
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 130px;
                right: 20px;
                background: ${type === 'info' ? 'linear-gradient(135deg, #667eea, #764ba2)' : '#e53e3e'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        function initializeAnimations() {
            // Add intersection observer for animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            // Observe all content cards
            document.querySelectorAll('.content-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        }

        // Enhanced hover effects for interactive items
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnimations();

            document.querySelectorAll('.interactive-item').forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05) translateY(-2px)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
