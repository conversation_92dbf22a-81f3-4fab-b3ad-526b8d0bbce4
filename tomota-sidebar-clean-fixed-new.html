<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Pond Report - Tomota Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        aqua: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        heading: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            background-color: #fafafa;
            color: #333;
            line-height: 1.5;
        }

        /* Sidebar Styles */
        .sidebar, .tomota-sidebar {
            width: 230px;
            height: 100vh;
            background-color: #f5f5f5;
            border-right: none;
            position: fixed;
            left: 0 !important;
            top: 0;
            margin-left: 0 !important;
            z-index: 1200;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            font-family: "Helvetica", "Roboto", Arial, sans-serif;
            transition: transform 0.3s ease;
        }

        .tomota-sidebar.hidden {
            transform: translateX(-100%);
        }

        .sidebar-logo {
            padding: 16px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
            background-color: #f5f5f5;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 77px;
        }

        .logo {
            width: 218px;
            height: 77px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border-radius: 4px;
            padding: 8px;
        }

        .logo-image {
            width: 160px;
            height: 100px;
            object-fit: contain;
            background-color: transparent;
        }

        .sidebar-nav {
            flex: 1;
            padding: 8px 0;
            overflow-y: auto;
            background-color: #f5f5f5;
        }

        .nav-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px 16px;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #565656;
            font-size: 14px;
            font-weight: 400;
            text-align: left;
            font-family: inherit;
        }

        .nav-item:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .nav-item.active {
            background-color: rgba(0, 0, 0, 0.08);
            color: #303030;
            font-weight: 500;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 400;
        }

        /* Sidebar Dropdown Styles */
        .nav-item-with-dropdown {
            position: relative;
        }

        .nav-dropdown-arrow {
            width: 16px;
            height: 16px;
            transition: transform 0.2s ease;
            flex-shrink: 0;
        }

        .nav-item-with-dropdown.open .nav-dropdown-arrow {
            transform: rotate(180deg);
        }

        .nav-dropdown {
            max-height: 0;
            overflow: hidden;
            background-color: #eeeeee;
            transition: max-height 0.3s ease, padding 0.3s ease;
            margin-left: 0;
        }

        .nav-dropdown.open {
            max-height: 200px;
            padding: 8px 0;
        }

        .nav-dropdown-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 10px 16px 10px 52px;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #565656;
            font-size: 13px;
            font-weight: 400;
            text-align: left;
            font-family: inherit;
        }

        .nav-dropdown-item:hover {
            background-color: rgba(0, 0, 0, 0.06);
        }

        .nav-dropdown-item.active {
            background-color: rgba(0, 0, 0, 0.1);
            color: #303030;
            font-weight: 500;
        }

        /* Header Styles */
        .header, .header-section {
            height: 110px;
            background: linear-gradient(90deg, rgba(0, 148, 255, 0.3) 0%, rgba(223, 216, 219, 0.3) 50%, rgba(255, 203, 191, 0.3) 100%);
            color: #fff;
            box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12);
            position: fixed;
            top: 0;
            left: 230px;
            right: 0;
            z-index: 9999;
            transition: left 0.3s ease;
        }

        .header-section.sidebar-hidden {
            left: 0;
        }

        .toolbar {
            display: flex;
            align-items: center;
            padding: 0 24px;
            min-height: 90px;
        }

        .toolbar-grid {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .left-section {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .menu-button {
            background: none;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            transition: background-color 0.2s ease;
            color: inherit;
        }

        .menu-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .menu-button svg {
            width: 32px;
            height: 32px;
            fill: currentColor;
        }

        .farm-info {
            color: black;
        }

        .header-icon-button {
            background: none;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            color: inherit;
        }

        .header-icon-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .header-icon-button img {
            width: 24px;
            height: 24px;
        }

        .avatar-button {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            background-color: #1976d2;
        }

        .avatar-button:hover {
            background-color: rgba(25, 118, 210, 0.8);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Main Content Area */
        .main-content {
            margin-left: 230px;
            margin-top: 110px;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-hidden {
            margin-left: 0;
        }

        .content-container {
            max-width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        /* Dashboard Styles */
        .dashboard-section {
            background: #f8f9fa;
            min-height: calc(100vh - 120px);
            padding: 0;
        }

        /* Dashboard Header Styles */
        .dashboard-header-section {
            background: white;
            padding: 0;
            margin: 0;
        }

        .dashboard-header-content {
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .farm-info-section {
            margin: 0;
        }

        .farm-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin: 0;
            line-height: 1.2;
        }

        .farm-subtitle {
            font-size: 14px;
            color: #666;
            margin: 4px 0 0 0;
        }

        /* Dashboard Main Content */
        .dashboard-main-content {
            background: white;
            margin: 0;
            padding: 0;
        }

        .home-section {
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .breadcrumb-section {
            margin-bottom: 12px;
        }

        .breadcrumb-text {
            font-size: 14px;
            color: #666;
        }

        .section-title {
            font-size: 32px;
            font-weight: 400;
            color: #333;
            margin: 0 0 20px 0;
        }

        /* Filters Row */
        .filters-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .season-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .filter-dropdown, .season-dropdown {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 32px 8px 12px;
            font-size: 14px;
            color: #333;
            min-width: 120px;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
        }

        .filter-dropdown:focus, .season-dropdown:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        /* Map Section */
        .map-section {
            background: white;
            margin: 0;
            padding: 0;
        }

        .map-controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .map-view-toggle {
            display: flex;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;
            overflow: hidden;
        }

        .map-toggle-btn {
            padding: 8px 16px;
            border: none;
            background: white;
            color: #666;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border-right: 1px solid #ddd;
        }

        .map-toggle-btn:last-child {
            border-right: none;
        }

        .map-toggle-btn.active {
            background: #f8f9fa;
            color: #333;
            font-weight: 600;
        }

        .map-toggle-btn:hover:not(.active) {
            background: #f5f5f5;
        }

        .fullscreen-map-btn {
            padding: 8px 12px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .fullscreen-map-btn:hover {
            background: #f5f5f5;
            color: #333;
        }

        .map-container-main {
            padding: 0;
            margin: 0;
        }

        .map-placeholder-content {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .filters-row {
                flex-direction: column;
                align-items: flex-start;
            }

            .filter-item {
                width: 100%;
            }

            .filter-dropdown, .season-dropdown {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Tomota Sidebar -->
    <div class="tomota-sidebar">
        <!-- Logo Section -->
        <div class="sidebar-logo">
            <div class="logo-container">
                <div class="logo">
                    <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/Ferris%20wheel.svg" alt="Ferris Wheel Logo" class="logo-image">
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <button class="nav-item" onclick="setActive(this); showDashboard();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-home-page-64.png" alt="Dashboard" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Dashboard</span>
            </button>

            <button class="nav-item active" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <svg style="width: 34px; height: 30px; fill: #666;" viewBox="0 0 24 24">
                        <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
                    </svg>
                </div>
                <span class="nav-text">Active pond report</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-ranking-100%20(1).png" alt="Ponds ranking" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Ponds ranking</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-stir-48.png" alt="Food mixing center" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Food mixing center</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-worker-67.png" alt="Work management" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Work management</span>
            </button>

            <div class="nav-item-with-dropdown">
                <button class="nav-item" onclick="toggleDropdown(this, 'farm-system');">
                    <div class="nav-icon">
                        <img src="file:///C:/Users/<USER>/Downloads/location.png" alt="Farm System" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                    </div>
                    <span class="nav-text">Farm System</span>
                    <svg class="nav-dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5H7z"/>
                    </svg>
                </button>
                <div class="nav-dropdown">
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showFarm();">
                        <span>Farm</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showPond();">
                        <span>Pond</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showShrimpDiary();">
                        <span>Shrimp Diary</span>
                    </button>
                </div>
            </div>

            <div class="nav-item-with-dropdown">
                <button class="nav-item" onclick="toggleDropdown(this, 'payment');">
                    <div class="nav-icon">
                        <img src="file:///C:/Users/<USER>/Downloads/—Pngtree—indian%20rupee%20note%20icon%20png_6668571.png" alt="Payment" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                    </div>
                    <span class="nav-text">Payment</span>
                    <svg class="nav-dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5H7z"/>
                    </svg>
                </button>
                <div class="nav-dropdown">
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showMine();">
                        <span>Mine</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showStaff();">
                        <span>Staff</span>
                    </button>
                </div>
            </div>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/Settings%20wheel.svg" alt="Setting" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Setting</span>
            </button>
        </nav>
    </div>

    <!-- Header Section -->
    <div class="header-section">
        <div class="toolbar">
            <div class="toolbar-grid">
                <div class="left-section">
                    <button class="menu-button" onclick="toggleSidebar()">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </button>
                    <div class="farm-info">
                        <div style="font-size: 12px; opacity: 0.8;">Current Farm</div>
                        <div style="font-size: 16px; font-weight: 500;">Sikindar Shaik</div>
                    </div>
                </div>
                <div class="right-section">
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/bell-svgrepo-com.svg" alt="Bell" style="height: 34px; width: 30px;">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/Ferris wheel.svg" alt="Ferris Wheel" style="height: 34px; width: 30px;">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/icons8-alert-48.png" alt="Alert" style="height: 24px; width: 24px;">
                    </button>
                    <img src="https://upload.wikimedia.org/wikipedia/commons/4/41/Flag_of_India.svg" alt="Flag of India" style="width: 32px; height: 32px; border-radius: 4px; object-fit: cover;">
                    <div class="user-dropdown">
                        <button class="avatar-button" onclick="toggleUserDropdown()">
                            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: 18px;">
                                S
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
        <div class="content-container">
            <!-- Dashboard Content -->
            <div id="dashboard-content" class="dashboard-section" style="display: none;">
                <!-- Header Section -->
                <div class="dashboard-header-section">
                    <div class="dashboard-header-content">
                        <div class="farm-info-section">
                            <h1 class="farm-title">Farm</h1>
                            <p class="farm-subtitle">All enterprise</p>
                        </div>
                    </div>
                </div>

                <!-- Main Content Section -->
                <div class="dashboard-main-content">
                    <!-- Home Section -->
                    <div class="home-section">
                        <div class="breadcrumb-section">
                            <span class="breadcrumb-text">Home</span>
                        </div>
                        <h2 class="section-title">Home</h2>
                        
                        <!-- Filters Row -->
                        <div class="filters-row">
                            <div class="filter-item">
                                <select class="filter-dropdown">
                                    <option>Farm</option>
                                    <option>Farm 1</option>
                                    <option>Farm 2</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <select class="filter-dropdown">
                                    <option>Pond</option>
                                    <option>Pond 1</option>
                                    <option>Pond 2</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <span class="season-label">Season</span>
                                <select class="filter-dropdown season-dropdown">
                                    <option>Vu 2-2025</option>
                                    <option>Vu 1-2025</option>
                                    <option>Vu 2-2024</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Map Section -->
                    <div class="map-section">
                        <div class="map-controls-header">
                            <div class="map-view-toggle">
                                <button class="map-toggle-btn active" onclick="switchMapView('map')">Map</button>
                                <button class="map-toggle-btn" onclick="switchMapView('satellite')">Satellite</button>
                            </div>
                            <button class="fullscreen-map-btn" onclick="toggleMapFullscreen()">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                        <div class="map-container-main">
                            <div class="map-placeholder-content">
                                <div style="width: 100%; height: 400px; background: linear-gradient(135deg, #2d5a3d 0%, #1a3d2e 50%, #0f2419 100%); border-radius: 0; position: relative;">
                                    <!-- Map content would go here -->
                                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; text-align: center;">
                                        <i class="fas fa-map text-4xl mb-3"></i>
                                        <p>Interactive Map View</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Regular Content Tabs and Tables -->
            <div class="content-header">
                <h2>Active Pond Report</h2>
                <div class="controls">
                    <div class="farm-selector">
                        <label>Farm</label>
                        <select>
                            <option>Sikindar Shaik</option>
                        </select>
                    </div>
                    <div class="date-controls">
                        <input type="date" value="2024-01-01">
                        <button><i class="fas fa-chevron-left"></i></button>
                        <button><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>
            </div>

            <div class="content-tabs">
                <button class="content-tab active" onclick="switchTab('active-pond', this)">Active Pond Overview</button>
                <button class="content-tab" onclick="switchTab('farm-report', this)">Farm Report</button>
            </div>

            <div class="legend">
                <div class="legend-item">
                    <span class="legend-color active-pond"></span>
                    Active Pond
                </div>
                <div class="legend-item">
                    <span class="legend-color stocking-pond"></span>
                    Stocking
                </div>
                <div class="legend-item">
                    <span class="legend-color renovation-pond"></span>
                    Renovation
                </div>
                <div class="legend-item">
                    <span class="legend-color risk-pond-1"></span>
                    Risk Level 1
                </div>
                <div class="legend-item">
                    <span class="legend-color risk-pond-2"></span>
                    Risk Level 2
                </div>
                <div class="legend-item">
                    <span class="legend-color risk-pond-3"></span>
                    Risk Level 3
                </div>
            </div>

            <div id="active-pond-content" class="tab-content">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Pond</th>
                                <th>Day 1</th>
                                <th>Day 2</th>
                                <th>Day 3</th>
                                <th>Day 4</th>
                                <th>Day 5</th>
                                <th>Day 6</th>
                                <th>Day 7</th>
                                <th>Day 8</th>
                                <th>Day 9</th>
                                <th>Day 10</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>A1</strong></td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                                <td class="pond-cell active-pond">Active</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setActive(element) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            element.classList.add('active');
        }

        function toggleDropdown(element, dropdownType) {
            const dropdownContainer = element.parentElement;
            const dropdown = dropdownContainer.querySelector('.nav-dropdown');
            
            // Close other dropdowns first
            const allDropdowns = document.querySelectorAll('.nav-item-with-dropdown');
            allDropdowns.forEach(item => {
                if (item !== dropdownContainer) {
                    item.classList.remove('open');
                    item.querySelector('.nav-dropdown').classList.remove('open');
                }
            });
            
            // Toggle current dropdown
            dropdownContainer.classList.toggle('open');
            dropdown.classList.toggle('open');
            
            // Set active state for parent nav item
            setActive(element);
        }

        function setActiveDropdownItem(element) {
            // Remove active class from all dropdown items in this dropdown
            const dropdown = element.closest('.nav-dropdown');
            const dropdownItems = dropdown.querySelectorAll('.nav-dropdown-item');
            dropdownItems.forEach(item => item.classList.remove('active'));
            
            // Add active class to clicked item
            element.classList.add('active');
        }

        function showDashboard() {
            // Hide other content sections
            const tabsContent = document.querySelector('.content-container > .content-header');
            const dashboardContent = document.getElementById('dashboard-content');
            
            if (tabsContent) {
                tabsContent.parentElement.style.display = 'none';
            }
            
            // Show dashboard content
            if (dashboardContent) {
                dashboardContent.style.display = 'block';
            }
            
            console.log('Dashboard selected');
        }

        function showTabs() {
            // Hide dashboard content
            const dashboardContent = document.getElementById('dashboard-content');
            const tabsContent = document.querySelector('.content-container');
            
            if (dashboardContent) {
                dashboardContent.style.display = 'none';
            }
            
            // Show tabs content
            if (tabsContent) {
                tabsContent.style.display = 'block';
            }
            
            console.log('Tabs content selected');
        }

        // Farm System dropdown functions
        function showFarm() {
            console.log('Farm selected');
        }

        function showPond() {
            console.log('Pond selected');
        }

        function showShrimpDiary() {
            console.log('Shrimp Diary selected');
        }

        // Payment dropdown functions
        function showMine() {
            console.log('Mine selected');
        }

        function showStaff() {
            console.log('Staff selected');
        }

        function toggleSidebar() {
            const sidebar = document.querySelector('.tomota-sidebar');
            const mainContent = document.querySelector('.main-content');
            const header = document.querySelector('.header-section');

            sidebar.classList.toggle('hidden');
            mainContent.classList.toggle('sidebar-hidden');
            header.classList.toggle('sidebar-hidden');
        }

        function switchTab(tabName, element) {
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });

            const tabs = document.querySelectorAll('.content-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            const selectedContent = document.getElementById(tabName + '-content');
            if (selectedContent) {
                selectedContent.style.display = 'block';
            }

            element.classList.add('active');
        }

        function switchMapView(viewType) {
            const buttons = document.querySelectorAll('.map-toggle-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            console.log('Switched to', viewType, 'view');
        }

        function toggleMapFullscreen() {
            const mapContainer = document.querySelector('.map-container-main');
            console.log('Toggle fullscreen for map');
        }
    </script>
</body>
</html>
