<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Pond Report - Tomota Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            background-color: #fafafa;
            color: #333;
            line-height: 1.5;
        }

        /* Sidebar Styles */
        .tomota-sidebar {
            width: 230px;
            height: 100vh;
            background-color: #f5f5f5;
            border-right: none;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1200;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            font-family: "Helvetica", "Roboto", Arial, sans-serif;
            transition: transform 0.3s ease;
        }

        .tomota-sidebar.hidden {
            transform: translateX(-100%);
        }

        .sidebar-logo {
            padding: 16px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
            background-color: #f5f5f5;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 77px;
        }

        .logo {
            width: 218px;
            height: 77px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border-radius: 4px;
            padding: 8px;
        }

        .logo-image {
            width: 160px;
            height: 100px;
            object-fit: contain;
            background-color: transparent;
        }

        .sidebar-nav {
            flex: 1;
            padding: 8px 0;
            overflow-y: auto;
            background-color: #f5f5f5;
        }

        .nav-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px 16px;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #565656;
            font-size: 14px;
            font-weight: 400;
            text-align: left;
            font-family: inherit;
        }

        .nav-item:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .nav-item.active {
            background-color: rgba(0, 0, 0, 0.08);
            color: #303030;
            font-weight: 500;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 400;
        }

        /* Dropdown Styles */
        .nav-item-with-dropdown {
            position: relative;
        }

        .nav-dropdown-arrow {
            width: 16px;
            height: 16px;
            transition: transform 0.2s ease;
            flex-shrink: 0;
        }

        .nav-item-with-dropdown.open .nav-dropdown-arrow {
            transform: rotate(180deg);
        }

        .nav-dropdown {
            max-height: 0;
            overflow: hidden;
            background-color: #eeeeee;
            transition: max-height 0.3s ease, padding 0.3s ease;
            margin-left: 0;
        }

        .nav-dropdown.open {
            max-height: 200px;
            padding: 8px 0;
        }

        .nav-dropdown-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 10px 16px 10px 52px;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #565656;
            font-size: 13px;
            font-weight: 400;
            text-align: left;
            font-family: inherit;
        }

        .nav-dropdown-item:hover {
            background-color: rgba(0, 0, 0, 0.06);
        }

        .nav-dropdown-item.active {
            background-color: rgba(0, 0, 0, 0.1);
            color: #303030;
            font-weight: 500;
        }

        /* Header Styles */
        .header-section {
            height: 110px;
            background: linear-gradient(90deg, rgba(0, 148, 255, 0.3) 0%, rgba(223, 216, 219, 0.3) 50%, rgba(255, 203, 191, 0.3) 100%);
            color: #fff;
            box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12);
            position: fixed;
            top: 0;
            left: 230px;
            right: 0;
            z-index: 9999;
            transition: left 0.3s ease;
        }

        .header-section.sidebar-hidden {
            left: 0;
        }

        .toolbar {
            display: flex;
            align-items: center;
            padding: 0 24px;
            min-height: 90px;
        }

        .toolbar-grid {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .left-section {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .menu-button {
            background: none;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            transition: background-color 0.2s ease;
            color: inherit;
        }

        .menu-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .menu-button svg {
            width: 32px;
            height: 32px;
            fill: currentColor;
        }

        .farm-info {
            color: black;
        }

        .header-icon-button {
            background: none;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            color: inherit;
        }

        .header-icon-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .avatar-button {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            background-color: #1976d2;
        }

        .avatar-button:hover {
            background-color: rgba(25, 118, 210, 0.8);
        }

        /* Main Content Area */
        .main-content {
            margin-left: 230px;
            margin-top: 110px;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-hidden {
            margin-left: 0;
        }

        .content-container {
            max-width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        /* Dashboard Styles */
        .dashboard-section {
            background: #f8f9fa;
            min-height: calc(100vh - 120px);
            padding: 0;
        }

        .dashboard-header-section {
            background: white;
            padding: 0;
            margin: 0;
        }

        .dashboard-header-content {
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .farm-info-section {
            margin: 0;
        }

        .farm-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin: 0;
            line-height: 1.2;
        }

        .farm-subtitle {
            font-size: 14px;
            color: #666;
            margin: 4px 0 0 0;
        }

        .dashboard-main-content {
            background: white;
            margin: 0;
            padding: 0;
        }

        .home-section {
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .breadcrumb-section {
            margin-bottom: 12px;
        }

        .breadcrumb-text {
            font-size: 14px;
            color: #666;
        }

        .section-title {
            font-size: 32px;
            font-weight: 400;
            color: #333;
            margin: 0 0 20px 0;
        }

        .filters-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .season-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .filter-dropdown, .season-dropdown {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 32px 8px 12px;
            font-size: 14px;
            color: #333;
            min-width: 120px;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
        }

        .filter-dropdown:focus, .season-dropdown:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .map-section {
            background: white;
            margin: 0;
            padding: 0;
        }

        .map-controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .map-view-toggle {
            display: flex;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;
            overflow: hidden;
        }

        .map-toggle-btn {
            padding: 8px 16px;
            border: none;
            background: white;
            color: #666;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border-right: 1px solid #ddd;
        }

        .map-toggle-btn:last-child {
            border-right: none;
        }

        .map-toggle-btn.active {
            background: #f8f9fa;
            color: #333;
            font-weight: 600;
        }

        .map-toggle-btn:hover:not(.active) {
            background: #f5f5f5;
        }

        .fullscreen-map-btn {
            padding: 8px 12px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .fullscreen-map-btn:hover {
            background: #f5f5f5;
            color: #333;
        }

        .map-container-main {
            padding: 0;
            margin: 0;
        }

        .map-placeholder-content {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        /* Regular Content Styles */
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
            background: white;
        }

        .content-header h2 {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin: 0;
        }

        .controls {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .farm-selector {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .farm-selector label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .farm-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background: white;
        }

        .date-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
        }

        .date-controls button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .date-controls button:hover {
            background: #f5f5f5;
        }

        .content-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .content-tab {
            padding: 12px 24px;
            border: none;
            background: none;
            color: #666;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
        }

        .content-tab:hover {
            background: rgba(0, 0, 0, 0.04);
        }

        .content-tab.active {
            background: white;
            color: #333;
            border-bottom-color: #007bff;
        }

        .legend {
            display: flex;
            gap: 20px;
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }

        .legend-color.active-pond {
            background-color: #4caf50;
        }

        .legend-color.stocking-pond {
            background-color: #2196f3;
        }

        .legend-color.renovation-pond {
            background-color: #ff9800;
        }

        .legend-color.risk-pond-1 {
            background-color: #ffeb3b;
        }

        .legend-color.risk-pond-2 {
            background-color: #ff5722;
        }

        .legend-color.risk-pond-3 {
            background-color: #f44336;
        }

        .tab-content {
            padding: 24px;
            background: white;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .pond-cell {
            padding: 8px 12px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            text-align: center;
        }

        .pond-cell.active-pond {
            background-color: #4caf50;
        }

        @media (max-width: 768px) {
            .filters-row {
                flex-direction: column;
                align-items: flex-start;
            }

            .filter-item {
                width: 100%;
            }

            .filter-dropdown, .season-dropdown {
                width: 100%;
            }

            .legend {
                flex-direction: column;
            }

            .controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="tomota-sidebar">
        <div class="sidebar-logo">
            <div class="logo-container">
                <div class="logo">
                    <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/Ferris%20wheel.svg" alt="Ferris Wheel Logo" class="logo-image">
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <button class="nav-item" onclick="setActive(this); showDashboard();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-home-page-64.png" alt="Dashboard" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Dashboard</span>
            </button>

            <button class="nav-item active" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <svg style="width: 34px; height: 30px; fill: #666;" viewBox="0 0 24 24">
                        <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
                    </svg>
                </div>
                <span class="nav-text">Active pond report</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-ranking-100%20(1).png" alt="Ponds ranking" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Ponds ranking</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-stir-48.png" alt="Food mixing center" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Food mixing center</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-worker-67.png" alt="Work management" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Work management</span>
            </button>

            <div class="nav-item-with-dropdown">
                <button class="nav-item" onclick="toggleDropdown(this, 'farm-system');">
                    <div class="nav-icon">
                        <img src="file:///C:/Users/<USER>/Downloads/location.png" alt="Farm System" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                    </div>
                    <span class="nav-text">Farm System</span>
                    <svg class="nav-dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5H7z"/>
                    </svg>
                </button>
                <div class="nav-dropdown">
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showFarm();">
                        <span>Farm</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showPond();">
                        <span>Pond</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showShrimpDiary();">
                        <span>Shrimp Diary</span>
                    </button>
                </div>
            </div>

            <div class="nav-item-with-dropdown">
                <button class="nav-item" onclick="toggleDropdown(this, 'payment');">
                    <div class="nav-icon">
                        <img src="file:///C:/Users/<USER>/Downloads/—Pngtree—indian%20rupee%20note%20icon%20png_6668571.png" alt="Payment" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                    </div>
                    <span class="nav-text">Payment</span>
                    <svg class="nav-dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5H7z"/>
                    </svg>
                </button>
                <div class="nav-dropdown">
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showMine();">
                        <span>Mine</span>
                    </button>
                    <button class="nav-dropdown-item" onclick="setActiveDropdownItem(this); showStaff();">
                        <span>Staff</span>
                    </button>
                </div>
            </div>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/Settings%20wheel.svg" alt="Setting" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Setting</span>
            </button>
        </nav>
    </div>

    <!-- Header -->
    <div class="header-section">
        <div class="toolbar">
            <div class="toolbar-grid">
                <div class="left-section">
                    <button class="menu-button" onclick="toggleSidebar()">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </button>
                    <div class="farm-info">
                        <div style="font-size: 12px; opacity: 0.8;">Current Farm</div>
                        <div style="font-size: 16px; font-weight: 500;">Sikindar Shaik</div>
                    </div>
                </div>
                <div class="right-section">
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/bell-svgrepo-com.svg" alt="Bell" style="height: 34px; width: 30px;">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/Ferris wheel.svg" alt="Ferris Wheel" style="height: 34px; width: 30px;">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/icons8-alert-48.png" alt="Alert" style="height: 24px; width: 24px;">
                    </button>
                    <img src="https://upload.wikimedia.org/wikipedia/commons/4/41/Flag_of_India.svg" alt="Flag of India" style="width: 32px; height: 32px; border-radius: 4px; object-fit: cover;">
                    <button class="avatar-button">
                        <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: 18px;">
                            S
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-container">
            <!-- Dashboard Content -->
            <div id="dashboard-content" class="dashboard-section" style="display: none;">
                <div class="dashboard-header-section">
                    <div class="dashboard-header-content">
                        <div class="farm-info-section">
                            <h1 class="farm-title">Farm</h1>
                            <p class="farm-subtitle">All enterprise</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-main-content">
                    <div class="home-section">
                        <div class="breadcrumb-section">
                            <span class="breadcrumb-text">Home</span>
                        </div>
                        <h2 class="section-title">Home</h2>
                        
                        <div class="filters-row">
                            <div class="filter-item">
                                <select class="filter-dropdown">
                                    <option>Farm</option>
                                    <option>Farm 1</option>
                                    <option>Farm 2</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <select class="filter-dropdown">
                                    <option>Pond</option>
                                    <option>Pond 1</option>
                                    <option>Pond 2</option>
                                </select>
                            </div>
                            <div class="filter-item">
                                <span class="season-label">Season</span>
                                <select class="filter-dropdown season-dropdown">
                                    <option>Vu 2-2025</option>
                                    <option>Vu 1-2025</option>
                                    <option>Vu 2-2024</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="map-section">
                        <div class="map-controls-header">
                            <div class="map-view-toggle">
                                <button class="map-toggle-btn active" onclick="switchMapView('map')">Map</button>
                                <button class="map-toggle-btn" onclick="switchMapView('satellite')">Satellite</button>
                            </div>
                            <button class="fullscreen-map-btn" onclick="toggleMapFullscreen()">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                        <div class="map-container-main">
                            <div class="map-placeholder-content">
                                <div style="width: 100%; height: 400px; background: linear-gradient(135deg, #2d5a3d 0%, #1a3d2e 50%, #0f2419 100%); border-radius: 0; position: relative;">
                                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; text-align: center;">
                                        <i class="fas fa-map text-4xl mb-3"></i>
                                        <p>Interactive Map View</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Regular Content -->
            <div id="regular-content">
                <div class="content-header">
                    <h2>Active Pond Report</h2>
                    <div class="controls">
                        <div class="farm-selector">
                            <label>Farm</label>
                            <select>
                                <option>Sikindar Shaik</option>
                            </select>
                        </div>
                        <div class="date-controls">
                            <input type="date" value="2024-01-01">
                            <button><i class="fas fa-chevron-left"></i></button>
                            <button><i class="fas fa-chevron-right"></i></button>
                        </div>
                    </div>
                </div>

                <div class="content-tabs">
                    <button class="content-tab active" onclick="switchTab('active-pond', this)">Active Pond Overview</button>
                    <button class="content-tab" onclick="switchTab('farm-report', this)">Farm Report</button>
                </div>

                <div class="legend">
                    <div class="legend-item">
                        <span class="legend-color active-pond"></span>
                        Active Pond
                    </div>
                    <div class="legend-item">
                        <span class="legend-color stocking-pond"></span>
                        Stocking
                    </div>
                    <div class="legend-item">
                        <span class="legend-color renovation-pond"></span>
                        Renovation
                    </div>
                    <div class="legend-item">
                        <span class="legend-color risk-pond-1"></span>
                        Risk Level 1
                    </div>
                    <div class="legend-item">
                        <span class="legend-color risk-pond-2"></span>
                        Risk Level 2
                    </div>
                    <div class="legend-item">
                        <span class="legend-color risk-pond-3"></span>
                        Risk Level 3
                    </div>
                </div>

                <div id="active-pond-content" class="tab-content">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Pond</th>
                                    <th>Day 1</th>
                                    <th>Day 2</th>
                                    <th>Day 3</th>
                                    <th>Day 4</th>
                                    <th>Day 5</th>
                                    <th>Day 6</th>
                                    <th>Day 7</th>
                                    <th>Day 8</th>
                                    <th>Day 9</th>
                                    <th>Day 10</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>A1</strong></td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                    <td class="pond-cell active-pond">Active</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setActive(element) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            element.classList.add('active');
        }

        function toggleDropdown(element, dropdownType) {
            const dropdownContainer = element.parentElement;
            const dropdown = dropdownContainer.querySelector('.nav-dropdown');
            
            const allDropdowns = document.querySelectorAll('.nav-item-with-dropdown');
            allDropdowns.forEach(item => {
                if (item !== dropdownContainer) {
                    item.classList.remove('open');
                    item.querySelector('.nav-dropdown').classList.remove('open');
                }
            });
            
            dropdownContainer.classList.toggle('open');
            dropdown.classList.toggle('open');
            setActive(element);
        }

        function setActiveDropdownItem(element) {
            const dropdown = element.closest('.nav-dropdown');
            const dropdownItems = dropdown.querySelectorAll('.nav-dropdown-item');
            dropdownItems.forEach(item => item.classList.remove('active'));
            element.classList.add('active');
        }

        function showDashboard() {
            document.getElementById('dashboard-content').style.display = 'block';
            document.getElementById('regular-content').style.display = 'none';
        }

        function showTabs() {
            document.getElementById('dashboard-content').style.display = 'none';
            document.getElementById('regular-content').style.display = 'block';
        }

        function showFarm() { console.log('Farm selected'); }
        function showPond() { console.log('Pond selected'); }
        function showShrimpDiary() { console.log('Shrimp Diary selected'); }
        function showMine() { console.log('Mine selected'); }
        function showStaff() { console.log('Staff selected'); }

        function toggleSidebar() {
            const sidebar = document.querySelector('.tomota-sidebar');
            const mainContent = document.querySelector('.main-content');
            const header = document.querySelector('.header-section');

            sidebar.classList.toggle('hidden');
            mainContent.classList.toggle('sidebar-hidden');
            header.classList.toggle('sidebar-hidden');
        }

        function switchTab(tabName, element) {
            const tabs = document.querySelectorAll('.content-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            element.classList.add('active');
        }

        function switchMapView(viewType) {
            const buttons = document.querySelectorAll('.map-toggle-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function toggleMapFullscreen() {
            console.log('Toggle fullscreen for map');
        }

        // Initialize with tabs content showing
        showTabs();
    </script>
</body>
</html>
